2025-06-21 23:10:21,828 - unicode_test - INFO - [SUCCESS] Unicode-safe logging configured
2025-06-21 23:10:25,246 - unicode_test - INFO - [SUCCESS] Unicode-safe logging configured
2025-06-21 23:10:25,246 - main - INFO - Successfully configured Unicode-safe logging
2025-06-21 23:10:25,247 - main - INFO - Starting bot...
2025-06-21 23:10:25,248 - helius_rate_limiter - INFO - Helius Rate Limiter initialized: 50.0 RPS
2025-06-21 23:10:25,248 - main - INFO - [SUCCESS] BULLETPROOF SEPARATION: Analysis and Position Monitoring functions initialized
2025-06-21 23:10:25,248 - main - INFO - [SUCCESS] PERMANENT TRACKING: 0 analyzed, 0 skipped
2025-06-21 23:10:25,252 - pumpportal_trader - DEBUG - Using MAINNET RPC: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-21 23:10:25,252 - pumpportal_trader - INFO - Loaded transaction settings: {}
2025-06-21 23:10:25,254 - pumpportal_trader - INFO - Loaded configured default wallet: TEST1 with public key: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-21 23:10:25,254 - pumpportal_trader - INFO - PumpPortal Trader initialized for mainnet pump.fun trading
2025-06-21 23:10:25,254 - pumpportal_trader - INFO - Mainnet API URL: https://pumpportal.fun/api/trade-local
2025-06-21 23:10:25,254 - pumpportal_trader - INFO - [WARNING] Note: PumpPortal only supports mainnet
2025-06-21 23:10:25,254 - pumpportal_trader - INFO - RPC URL: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-21 23:10:25,254 - pumpportal_trader - INFO - Helius rate limiter: 50.0 RPS
2025-06-21 23:10:25,258 - unified_trader - INFO - Unified Trader initialized for mainnet real trading with adaptive slippage support
2025-06-21 23:10:25,258 - main - INFO - CRITICAL DEBUG: SIMPLE_PUMP_AVAILABLE = True
2025-06-21 23:10:25,258 - main - INFO - CRITICAL DEBUG: _fast_analysis_function = <function init_simple_pump_analyzer.<locals>.analyze_new_signal_only at 0x000001FADF12F880>
2025-06-21 23:10:25,258 - main - INFO - CRITICAL DEBUG: _position_monitoring_function = <function init_simple_pump_analyzer.<locals>.monitor_position_only at 0x000001FADF12F920>
2025-06-21 23:10:25,259 - main - INFO - [SUCCESS] Fast analysis function connected to bot controller (NEW SIGNALS ONLY)
2025-06-21 23:10:25,259 - main - INFO - [SUCCESS] Position monitoring function connected to bot controller (BOUGHT POSITIONS ONLY)
2025-06-21 23:10:25,259 - main - INFO - [AIM] MULTIPLIER FILTERING: ENABLED - Will reject tokens with 5x+ gains (allowing up to 5x multiplier)
2025-06-21 23:10:25,259 - main - INFO - Initializing DEX adapters and sessions
2025-06-21 23:10:25,260 - main - INFO - DEX sessions initialized successfully
2025-06-21 23:10:25,260 - main - INFO - LLM integration is not available in this configuration
2025-06-21 23:10:25,260 - main - INFO - Registered direct signal callback for faster signal proyescessing
2025-06-21 23:10:25,260 - main - INFO - LLM integration is not available in this configuration
2025-06-21 23:10:25,261 - main - INFO - Initializing bot controller WITHOUT signal processing...
2025-06-21 23:10:25,861 - main - INFO - [SUCCESS] Bot controller initialized successfully WITHOUT signal processing
2025-06-21 23:10:25,861 - main - INFO - Started cache cleanup task
2025-06-21 23:10:25,876 - production_monitor - INFO - Production monitoring started
2025-06-21 23:10:25,876 - main - INFO - [SUCCESS] Production monitoring started
2025-06-21 23:10:25,880 - error_tracker - INFO - [SUCCESS] Error tracking enabled
2025-06-21 23:10:25,880 - main - INFO - [SUCCESS] Error tracking enabled
2025-06-21 23:10:25,884 - main - INFO - [SUCCESS] Trading metrics collector enabled
2025-06-21 23:10:25,884 - main - INFO - Starting CLI interface...
2025-06-21 23:10:25,889 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:10:26,050 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:10:26,059 - production_monitor - INFO - Production monitor started
2025-06-21 23:10:26,170 - production_monitor - DEBUG - System healthy - CPU: 8.5%, Memory: 75.8%, Positions: 0
2025-06-21 23:10:32,273 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:10:32,401 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:10:38,616 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:10:38,843 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:10:41,345 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:10:41,567 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:10:42,102 - pumpportal_trader - DEBUG - Using MAINNET RPC: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-21 23:10:42,102 - pumpportal_trader - INFO - Loaded transaction settings: {}
2025-06-21 23:10:42,103 - pumpportal_trader - INFO - Loaded configured default wallet: TEST1 with public key: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-21 23:10:42,104 - pumpportal_trader - INFO - PumpPortal Trader initialized for mainnet pump.fun trading
2025-06-21 23:10:42,104 - pumpportal_trader - INFO - Mainnet API URL: https://pumpportal.fun/api/trade-local
2025-06-21 23:10:42,104 - pumpportal_trader - INFO - [WARNING] Note: PumpPortal only supports mainnet
2025-06-21 23:10:42,104 - pumpportal_trader - INFO - RPC URL: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-21 23:10:42,104 - pumpportal_trader - INFO - Helius rate limiter: 50.0 RPS
2025-06-21 23:10:42,104 - unified_trader - INFO - [SUCCESS] PumpPortal trader initialized for mainnet
2025-06-21 23:10:42,104 - unified_trader - INFO - ℹ️ Helius trader disabled - devnet support removed
2025-06-21 23:10:42,104 - unified_trader - INFO - [SUCCESS] PumpPortal trader initialized successfully (mainnet only)
2025-06-21 23:10:42,105 - pumpportal_trader - DEBUG - Using MAINNET RPC: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-21 23:10:42,106 - pumpportal_trader - INFO - [REFRESH] PumpPortal trader set to MAINNET mode
2025-06-21 23:10:42,106 - pumpportal_trader - INFO - [NETWORK] Updated RPC URL: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-21 23:10:42,106 - pumpportal_trader - INFO - 🔗 API URL: https://pumpportal.fun/api/trade-local
2025-06-21 23:10:42,106 - unified_trader - INFO - [LAUNCH] Confirmed MAINNET mode - using PumpPortal for pump.fun
2025-06-21 23:10:44,627 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:10:45,006 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:11:26,277 - production_monitor - DEBUG - System healthy - CPU: 9.4%, Memory: 75.6%, Positions: 0
2025-06-21 23:11:42,234 - main - INFO - [FAST] Analyzing token: ********************************************
2025-06-21 23:11:42,235 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.dexscreener.com:443
2025-06-21 23:11:42,236 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:11:42,236 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.coingecko.com:443
2025-06-21 23:11:42,546 - urllib3.connectionpool - DEBUG - https://api.coingecko.com:443 "GET /api/v3/simple/price?ids=solana&vs_currencies=usd HTTP/1.1" 200 None
2025-06-21 23:11:42,547 - main - INFO - Fetched fresh SOL price: $139.2 (attempt 1)
2025-06-21 23:11:42,676 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:11:42,677 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:11:42,853 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:11:42,853 - main - DEBUG - Token supply success: 999,981,512
2025-06-21 23:11:42,854 - main - INFO - Fetched data - SOL price: $139.2, Supply: 999,981,512
2025-06-21 23:11:42,854 - main - INFO - DexScreener data available - Price: $7.114e-05, Volume 24h: $2,194
2025-06-21 23:11:42,854 - main - INFO - Using DexScreener liquidity: $26,334 (MC: $71,139)
2025-06-21 23:11:42,855 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:11:43,262 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:11:43,262 - main - DEBUG - Holders success: 20 holders
2025-06-21 23:11:43,263 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:11:43,690 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:11:43,691 - main - INFO - [FAST] Analysis completed in 1.46s
2025-06-21 23:11:43,692 - main - INFO - [SCAN] MULTIPLIER CHECK ENABLED: Checking ******************************************** for high multipliers (3x+)
2025-06-21 23:11:43,692 - main - INFO - [CHART] PRICE CHANGES: 5m: 24.7%, 1h: 24.7%, 6h: 24.7%, 24h: 24.7% | Max: 24.7%
2025-06-21 23:11:43,692 - main - INFO - [SUCCESS] NEW/LOW MULTIPLIER TOKEN: ******************************************** - 24.7% gain (excellent entry)
2025-06-21 23:11:43,694 - main - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for ********************************************
2025-06-21 23:11:43,694 - main - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $26,334, MC: $71,139, Whale: 21.9%
2025-06-21 23:11:43,694 - main - INFO - [SUCCESS] Fast analysis result: Price=$0.00007114, MC=$71,139, Liq=$26,334
2025-06-21 23:11:43,694 - main - DEBUG - PERMANENT STATE: 1 total, 0 skipped
2025-06-21 23:11:43,694 - main - DEBUG - Permanent decision recorded for ********************************************
2025-06-21 23:11:43,699 - pumpportal_trader - INFO - [MONEY] EXTERNAL FEES: Buy tip: 0.0001 SOL, Gas: 0.0001 SOL, Handling: 0.0000 SOL, Platform: 0.0000 SOL
2025-06-21 23:11:43,699 - pumpportal_trader - INFO - [MONEY] TOTAL EXTERNAL FEES: 0.0002 SOL
2025-06-21 23:11:43,700 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:11:43,926 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:11:43,926 - pumpportal_trader - DEBUG - Wallet balance: 0.062240 SOL
2025-06-21 23:11:43,926 - pumpportal_trader - INFO - [GREEN] REAL BUY: 0.0010 SOL of ******************************************** (slippage: 15.0%) + 0.0002 SOL fees - Balance: 0.0622 SOL
2025-06-21 23:11:43,927 - pumpportal_trader - DEBUG - Priority fee: 0.******** SOL (100000 lamports)
2025-06-21 23:11:43,927 - pumpportal_trader - INFO - [SCAN] DETAILED PumpPortal payload:
2025-06-21 23:11:43,927 - pumpportal_trader - INFO -    action: buy
2025-06-21 23:11:43,927 - pumpportal_trader - INFO -    mint: ********************************************
2025-06-21 23:11:43,927 - pumpportal_trader - INFO -    amount: 0.001 (original: 0.001)
2025-06-21 23:11:43,928 - pumpportal_trader - INFO -    denominatedInSol: true
2025-06-21 23:11:43,928 - pumpportal_trader - INFO -    slippage: 15
2025-06-21 23:11:43,928 - pumpportal_trader - INFO -    priorityFee: 0.0001 SOL
2025-06-21 23:11:43,928 - pumpportal_trader - INFO -    publicKey: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-21 23:11:43,928 - pumpportal_trader - INFO - [NETWORK] Using PumpPortal API: https://pumpportal.fun/api/trade-local (Network: MAINNET)
2025-06-21 23:11:43,929 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): pumpportal.fun:443
2025-06-21 23:11:45,250 - urllib3.connectionpool - DEBUG - https://pumpportal.fun:443 "POST /api/trade-local HTTP/1.1" 200 876
2025-06-21 23:11:45,250 - pumpportal_trader - INFO - [SIGNAL] PumpPortal API Response: 200
2025-06-21 23:11:45,251 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:11:45,423 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:11:45,423 - pumpportal_trader - INFO - Transaction sent successfully on attempt 1: 3HYeJtHhWebc2GA827mi3a1FEbxeZ1NkkgS2uuFVnU8ibuhZcwKJ94zumdKqsA1C26r1zNaHsvdpKDVZHRN87Ft4
2025-06-21 23:11:45,424 - pumpportal_trader - INFO - [SUCCESS] Transaction sent successfully: 3HYeJtHhWebc2GA827mi3a1FEbxeZ1NkkgS2uuFVnU8ibuhZcwKJ94zumdKqsA1C26r1zNaHsvdpKDVZHRN87Ft4
2025-06-21 23:11:45,424 - pumpportal_trader - INFO - [SCAN] CRITICAL FIX: Waiting for blockchain verification before confirming success...
2025-06-21 23:11:45,425 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:11:46,134 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:11:48,150 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:11:48,543 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:11:48,543 - pumpportal_trader - INFO - [SUCCESS] Transaction VERIFIED successful: 3HYeJtHhWebc2GA827mi3a1FEbxeZ1NkkgS2uuFVnU8ibuhZcwKJ94zumdKqsA1C26r1zNaHsvdpKDVZHRN87Ft4
2025-06-21 23:11:48,544 - pumpportal_trader - INFO - [SUCCESS] VERIFIED: Transaction confirmed on blockchain: 3HYeJtHhWebc2GA827mi3a1FEbxeZ1NkkgS2uuFVnU8ibuhZcwKJ94zumdKqsA1C26r1zNaHsvdpKDVZHRN87Ft4
2025-06-21 23:11:48,544 - pumpportal_trader - INFO - [SUCCESS] Buy successful: 3HYeJtHhWebc2GA827mi3a1FEbxeZ1NkkgS2uuFVnU8ibuhZcwKJ94zumdKqsA1C26r1zNaHsvdpKDVZHRN87Ft4 (Total cost: 0.0012 SOL)
2025-06-21 23:11:48,733 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:11:49,144 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:11:56,147 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:11:56,147 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:11:56,169 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:11:56,169 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:11:56,169 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00007114, Liquidity: $26,334
2025-06-21 23:11:57,480 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:11:57,481 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:11:57,513 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:11:57,513 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:11:57,513 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00007114, Liquidity: $26,334
2025-06-21 23:11:58,830 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:11:58,831 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:11:58,853 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:11:58,853 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:11:58,853 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00007114, Liquidity: $26,334
2025-06-21 23:12:00,170 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:00,170 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:00,193 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:00,193 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:00,193 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00007114, Liquidity: $26,334
2025-06-21 23:12:01,507 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:01,507 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:01,539 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:01,540 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:01,540 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00007114, Liquidity: $26,334
2025-06-21 23:12:02,847 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:02,847 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:02,876 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:02,877 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:02,877 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00007114, Liquidity: $26,334
2025-06-21 23:12:04,204 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:04,204 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:04,230 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:04,230 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:04,230 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00007114, Liquidity: $26,334
2025-06-21 23:12:05,533 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:05,533 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:05,553 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:05,553 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:05,553 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00007114, Liquidity: $26,334
2025-06-21 23:12:06,857 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:06,857 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:06,883 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:06,884 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:06,884 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00007114, Liquidity: $26,334
2025-06-21 23:12:07,674 - main - INFO - [FAST] Analyzing token: 5dDsticWTu5w6oJbhZQkZCzc33hCgutG6QPo6itGpump
2025-06-21 23:12:07,676 - main - DEBUG - Using cached SOL price: $139.2 (age: 25.4s)
2025-06-21 23:12:07,677 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:12:07,905 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:12:07,905 - main - DEBUG - Token supply success: 1,000,000,000
2025-06-21 23:12:08,018 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/5dDsticWTu5w6oJbhZQkZCzc33hCgutG6QPo6itGpump HTTP/1.1" 200 None
2025-06-21 23:12:08,018 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-21 23:12:08,019 - main - INFO - Fetched data - SOL price: $139.2, Supply: 1,000,000,000
2025-06-21 23:12:08,019 - main - INFO - DexScreener data available - Price: $1.322e-05, Volume 24h: $18,982
2025-06-21 23:12:08,019 - main - INFO - [SCAN] Looking for pump.fun pool for 5dDsticWTu5w6oJbhZQkZCzc33hCgutG6QPo6itGpump
2025-06-21 23:12:08,020 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:12:08,207 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:12:08,209 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:12:08,619 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:12:08,620 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:12:08,870 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:12:08,870 - main - DEBUG - Pool balance success: 23.453409 SOL
2025-06-21 23:12:08,871 - main - INFO - [SUCCESS] Found pool address: 7cPQcwiMm4TpLcQAfrWz1d4Cm4bbgbXcP19nABeVJsNX
2025-06-21 23:12:08,871 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:12:09,129 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:12:09,130 - main - DEBUG - Pool balance success: 23.453409 SOL
2025-06-21 23:12:09,130 - main - INFO - [MONEY] Pool SOL balance: 23.453409393
2025-06-21 23:12:09,130 - main - INFO - [CHART] Calculated liquidity: $6,366 (SOL: 23.453409393 * $139.2 * 1.95)
2025-06-21 23:12:09,131 - main - INFO - Found pool 7cPQcwiM... with 23.453409 SOL
2025-06-21 23:12:09,131 - main - INFO - Using calculated liquidity: $6,366 (x1.95)
2025-06-21 23:12:09,132 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:12:09,532 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:12:09,532 - main - DEBUG - Holders success: 20 holders
2025-06-21 23:12:09,533 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:12:09,920 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:12:09,921 - main - INFO - [FAST] Analysis completed in 2.25s
2025-06-21 23:12:09,921 - main - INFO - [SCAN] MULTIPLIER CHECK ENABLED: Checking 5dDsticWTu5w6oJbhZQkZCzc33hCgutG6QPo6itGpump for high multipliers (3x+)
2025-06-21 23:12:09,922 - main - INFO - [CHART] PRICE CHANGES: 5m: 220.0%, 1h: 220.0%, 6h: 220.0%, 24h: 220.0% | Max: 220.0%
2025-06-21 23:12:09,922 - main - INFO - [WARNING] 3-4X MULTIPLIER TOKEN: 5dDsticWTu5w6oJbhZQkZCzc33hCgutG6QPo6itGpump - 220.0% gain (allowing as it's <= 5x)
2025-06-21 23:12:09,923 - main - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 5dDsticWTu5w6oJbhZQkZCzc33hCgutG6QPo6itGpump
2025-06-21 23:12:09,923 - main - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $6,366 < $7,000
2025-06-21 23:12:09,927 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:09,927 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:09,946 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:09,947 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:09,947 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00007114, Liquidity: $26,334
2025-06-21 23:12:11,253 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:11,253 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:11,279 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:11,279 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:11,280 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00007114, Liquidity: $26,334
2025-06-21 23:12:12,593 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:12,593 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:12,622 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:12,623 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:12,623 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00007114, Liquidity: $26,334
2025-06-21 23:12:13,934 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:13,934 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:14,436 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:14,436 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:14,436 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00006509, Liquidity: $25,208
2025-06-21 23:12:15,742 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:15,742 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:15,761 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:15,762 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:15,762 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00006509, Liquidity: $25,208
2025-06-21 23:12:17,075 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:17,075 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:17,097 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:17,097 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:17,097 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00006509, Liquidity: $25,208
2025-06-21 23:12:18,410 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:18,412 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:18,433 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:18,434 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:18,434 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00006509, Liquidity: $25,208
2025-06-21 23:12:19,750 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:19,751 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:19,773 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:19,773 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:19,773 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00006509, Liquidity: $25,208
2025-06-21 23:12:21,086 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:21,086 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:21,111 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:21,112 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:21,112 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00006509, Liquidity: $25,208
2025-06-21 23:12:22,428 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:22,428 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:22,449 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:22,449 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:22,449 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00006509, Liquidity: $25,208
2025-06-21 23:12:23,766 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:23,766 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:23,792 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:23,792 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:23,792 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00006509, Liquidity: $25,208
2025-06-21 23:12:25,099 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:25,099 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:25,121 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:25,122 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:25,122 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00006509, Liquidity: $25,208
2025-06-21 23:12:26,392 - production_monitor - DEBUG - System healthy - CPU: 7.2%, Memory: 76.2%, Positions: 0
2025-06-21 23:12:26,425 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:26,425 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:26,444 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:26,445 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:26,445 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00006509, Liquidity: $25,208
2025-06-21 23:12:27,760 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:27,760 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:27,784 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:27,784 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:27,784 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00006509, Liquidity: $25,208
2025-06-21 23:12:29,099 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:29,099 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:29,119 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:29,120 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:29,120 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00006509, Liquidity: $25,208
2025-06-21 23:12:30,435 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:30,436 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:30,456 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:30,456 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:30,456 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00006509, Liquidity: $25,208
2025-06-21 23:12:31,759 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:31,759 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:31,784 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:31,784 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:31,784 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00006509, Liquidity: $25,208
2025-06-21 23:12:33,093 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:33,094 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:33,115 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:33,115 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:33,115 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00006509, Liquidity: $25,208
2025-06-21 23:12:34,432 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:34,432 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:34,452 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:34,452 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:34,452 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00006509, Liquidity: $25,208
2025-06-21 23:12:35,770 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:35,770 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:35,791 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:35,791 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:35,791 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00006509, Liquidity: $25,208
2025-06-21 23:12:37,095 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:37,095 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:37,118 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:37,118 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:37,118 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00006509, Liquidity: $25,208
2025-06-21 23:12:38,423 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:38,423 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:38,443 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:38,443 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:38,443 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00006509, Liquidity: $25,208
2025-06-21 23:12:39,749 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:39,749 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:39,773 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:39,773 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:39,773 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00006509, Liquidity: $25,208
2025-06-21 23:12:41,081 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:41,081 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:41,100 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:41,101 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:41,101 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00006509, Liquidity: $25,208
2025-06-21 23:12:42,412 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:42,412 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:42,450 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:42,450 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:42,450 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00006509, Liquidity: $25,208
2025-06-21 23:12:43,762 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:43,762 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:43,790 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:43,791 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:43,792 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00006509, Liquidity: $25,208
2025-06-21 23:12:45,097 - main - DEBUG - POSITION MONITORING: Getting price update for ********************************************
2025-06-21 23:12:45,098 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:45,527 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:45,527 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:45,527 - main - DEBUG - POSITION MONITORING: ******************************************** - Price: $0.00009438, Liquidity: $30,392
2025-06-21 23:12:45,532 - pumpportal_trader - INFO - [MONEY] SELL FEES: Gas: 0.0001 SOL, Handling: 1.0%, Platform: 1.0%
2025-06-21 23:12:45,532 - pumpportal_trader - DEBUG - [SCAN] Checking token balance for 31TxE8ky... in wallet 75eUxZsu...
2025-06-21 23:12:45,533 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:12:45,688 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:12:45,689 - pumpportal_trader - DEBUG - [SCAN] Found 1 token accounts for 31TxE8ky...
2025-06-21 23:12:45,689 - pumpportal_trader - INFO - [SUCCESS] Token balance found: 1940.629669 tokens (raw: **********, decimals: 6)
2025-06-21 23:12:45,690 - pumpportal_trader - INFO - [SCAN] Token balance check: 1940.629669 tokens for ********************************************
2025-06-21 23:12:45,690 - pumpportal_trader - INFO - [RED] REAL SELL: 0.724% (14.0568/1940.6297 tokens) of ********************************************
2025-06-21 23:12:45,690 - pumpportal_trader - DEBUG - Priority fee: 0.******** SOL (100000 lamports)
2025-06-21 23:12:45,690 - pumpportal_trader - INFO - [FIX] CRITICAL FIX: Keeping percentage as string: '0.724%' (PumpPortal requires string format)
2025-06-21 23:12:45,690 - pumpportal_trader - INFO - [SCAN] DETAILED PumpPortal payload:
2025-06-21 23:12:45,691 - pumpportal_trader - INFO -    action: sell
2025-06-21 23:12:45,691 - pumpportal_trader - INFO -    mint: ********************************************
2025-06-21 23:12:45,691 - pumpportal_trader - INFO -    amount: 0.724% (original: 0.724%)
2025-06-21 23:12:45,691 - pumpportal_trader - INFO -    denominatedInSol: false
2025-06-21 23:12:45,692 - pumpportal_trader - INFO -    slippage: 27
2025-06-21 23:12:45,692 - pumpportal_trader - INFO -    priorityFee: 0.0001 SOL
2025-06-21 23:12:45,692 - pumpportal_trader - INFO -    publicKey: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-21 23:12:45,692 - pumpportal_trader - INFO - [NETWORK] Using PumpPortal API: https://pumpportal.fun/api/trade-local (Network: MAINNET)
2025-06-21 23:12:45,693 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): pumpportal.fun:443
2025-06-21 23:12:46,920 - urllib3.connectionpool - DEBUG - https://pumpportal.fun:443 "POST /api/trade-local HTTP/1.1" 200 844
2025-06-21 23:12:46,920 - pumpportal_trader - INFO - [SIGNAL] PumpPortal API Response: 200
2025-06-21 23:12:46,921 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:12:47,097 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:12:47,098 - pumpportal_trader - INFO - Transaction sent successfully on attempt 1: 63MMZMqttsrueMJGzpjPd2vK9XRrNvsDp77wShQYFf6yELk5TAD8zoptSnGUAVeyriSoEkzisUEZVjvdYtECx8wd
2025-06-21 23:12:47,098 - pumpportal_trader - INFO - [SUCCESS] Transaction sent successfully: 63MMZMqttsrueMJGzpjPd2vK9XRrNvsDp77wShQYFf6yELk5TAD8zoptSnGUAVeyriSoEkzisUEZVjvdYtECx8wd
2025-06-21 23:12:47,098 - pumpportal_trader - INFO - [SCAN] CRITICAL FIX: Waiting for blockchain verification before confirming success...
2025-06-21 23:12:47,099 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:12:47,626 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:12:49,629 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:12:49,795 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:12:49,796 - pumpportal_trader - INFO - [SUCCESS] Transaction VERIFIED successful: 63MMZMqttsrueMJGzpjPd2vK9XRrNvsDp77wShQYFf6yELk5TAD8zoptSnGUAVeyriSoEkzisUEZVjvdYtECx8wd
2025-06-21 23:12:49,796 - pumpportal_trader - INFO - [SUCCESS] VERIFIED: Transaction confirmed on blockchain: 63MMZMqttsrueMJGzpjPd2vK9XRrNvsDp77wShQYFf6yELk5TAD8zoptSnGUAVeyriSoEkzisUEZVjvdYtECx8wd
2025-06-21 23:12:49,797 - pumpportal_trader - INFO - [SUCCESS] Sell successful: 63MMZMqttsrueMJGzpjPd2vK9XRrNvsDp77wShQYFf6yELk5TAD8zoptSnGUAVeyriSoEkzisUEZVjvdYtECx8wd (Gas cost: 0.0001 SOL)
2025-06-21 23:12:49,857 - main - DEBUG - POSITION MONITORING: Fresh analysis for ********************************************
2025-06-21 23:12:49,857 - main - INFO - [FAST] Analyzing token: ********************************************
2025-06-21 23:12:49,858 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: ********************************************
2025-06-21 23:12:49,883 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/******************************************** HTTP/1.1" 200 None
2025-06-21 23:12:49,883 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-21 23:12:50,150 - urllib3.connectionpool - DEBUG - https://api.coingecko.com:443 "GET /api/v3/simple/price?ids=solana&vs_currencies=usd HTTP/1.1" 200 None
2025-06-21 23:12:50,151 - main - INFO - Fetched fresh SOL price: $139.19 (attempt 1)
2025-06-21 23:12:50,151 - main - INFO - Fetched data - SOL price: $139.19, Supply: 999,981,512
2025-06-21 23:12:50,151 - main - INFO - DexScreener data available - Price: $9.438e-05, Volume 24h: $19,853
2025-06-21 23:12:50,152 - main - INFO - Using DexScreener liquidity: $30,392 (MC: $94,380)
2025-06-21 23:12:50,152 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:12:50,335 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:12:50,336 - main - DEBUG - Holders success: 20 holders
2025-06-21 23:12:50,336 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:12:50,513 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:12:50,515 - main - INFO - [FAST] Analysis completed in 0.66s
2025-06-21 23:12:50,517 - main - INFO - [SCAN] MULTIPLIER CHECK ENABLED: Checking ******************************************** for high multipliers (3x+)
2025-06-21 23:12:50,517 - main - INFO - [CHART] PRICE CHANGES: 5m: 65.5%, 1h: 65.5%, 6h: 65.5%, 24h: 65.5% | Max: 65.5%
2025-06-21 23:12:50,518 - main - INFO - [SUCCESS] NEW/LOW MULTIPLIER TOKEN: ******************************************** - 65.5% gain (excellent entry)
2025-06-21 23:12:50,522 - main - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for ********************************************
2025-06-21 23:12:50,523 - main - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $30,392, MC: $94,380, Whale: 21.3%
2025-06-21 23:12:50,523 - main - INFO - [SUCCESS] Fast analysis result: Price=$0.00009438, MC=$94,380, Liq=$30,392
2025-06-21 23:12:50,524 - main - DEBUG - PERMANENT STATE: 1 total, 0 skipped
2025-06-21 23:12:50,524 - main - DEBUG - Permanent decision recorded for ********************************************
2025-06-21 23:12:50,526 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:12:50,771 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:13:26,511 - production_monitor - DEBUG - System healthy - CPU: 5.4%, Memory: 75.9%, Positions: 0
2025-06-21 23:13:36,458 - main - INFO - [FAST] Analyzing token: GVkSYFMuLsFqGpkmdhFMMRswLpW7BND82XSSNRcupump
2025-06-21 23:13:36,459 - main - DEBUG - Using cached SOL price: $139.19 (age: 46.6s)
2025-06-21 23:13:36,460 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:13:36,630 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:13:36,630 - main - DEBUG - Token supply success: 1,000,000,000
2025-06-21 23:13:36,765 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/GVkSYFMuLsFqGpkmdhFMMRswLpW7BND82XSSNRcupump HTTP/1.1" 200 None
2025-06-21 23:13:36,765 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-21 23:13:36,766 - main - INFO - Fetched data - SOL price: $139.19, Supply: 1,000,000,000
2025-06-21 23:13:36,766 - main - INFO - DexScreener data available - Price: $1.166e-05, Volume 24h: $20,362
2025-06-21 23:13:36,766 - main - INFO - [SCAN] Looking for pump.fun pool for GVkSYFMuLsFqGpkmdhFMMRswLpW7BND82XSSNRcupump
2025-06-21 23:13:36,767 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:13:36,932 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:13:36,933 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:13:37,117 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:13:37,118 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:13:37,482 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:13:37,482 - main - DEBUG - Pool balance success: 24.600303 SOL
2025-06-21 23:13:37,483 - main - INFO - [SUCCESS] Found pool address: HZpaTij4Nmu1KgS8SKeposUnNnLzcF5q6gqseANmTX3A
2025-06-21 23:13:37,483 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:13:37,893 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:13:37,894 - main - DEBUG - Pool balance success: 24.394820 SOL
2025-06-21 23:13:37,894 - main - INFO - [MONEY] Pool SOL balance: 24.394819849
2025-06-21 23:13:37,894 - main - INFO - [CHART] Calculated liquidity: $6,621 (SOL: 24.394819849 * $139.19 * 1.95)
2025-06-21 23:13:37,894 - main - INFO - Found pool HZpaTij4... with 24.394820 SOL
2025-06-21 23:13:37,894 - main - INFO - Using calculated liquidity: $6,621 (x1.95)
2025-06-21 23:13:37,895 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:13:38,035 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:13:38,036 - main - DEBUG - Holders success: 20 holders
2025-06-21 23:13:38,036 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-21 23:13:38,512 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-21 23:13:38,513 - main - INFO - [FAST] Analysis completed in 2.06s
2025-06-21 23:13:38,514 - main - INFO - [SCAN] MULTIPLIER CHECK ENABLED: Checking GVkSYFMuLsFqGpkmdhFMMRswLpW7BND82XSSNRcupump for high multipliers (3x+)
2025-06-21 23:13:38,514 - main - INFO - [CHART] PRICE CHANGES: 5m: 165.0%, 1h: 165.0%, 6h: 165.0%, 24h: 165.0% | Max: 165.0%
2025-06-21 23:13:38,514 - main - INFO - [SUCCESS] 2X MULTIPLIER TOKEN: GVkSYFMuLsFqGpkmdhFMMRswLpW7BND82XSSNRcupump - 165.0% gain (good entry)
2025-06-21 23:13:38,516 - main - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for GVkSYFMuLsFqGpkmdhFMMRswLpW7BND82XSSNRcupump
2025-06-21 23:13:38,516 - main - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $6,621 < $7,000
2025-06-22 15:20:46,111 - unicode_test - INFO - [SUCCESS] Unicode-safe logging configured
2025-06-22 15:21:22,124 - unicode_test - INFO - [SUCCESS] Unicode-safe logging configured
2025-06-22 15:21:23,664 - unicode_test - INFO - [SUCCESS] Unicode-safe logging configured
2025-06-22 15:21:23,664 - main - INFO - Successfully configured Unicode-safe logging
2025-06-22 15:21:23,665 - main - INFO - Starting bot...
2025-06-22 15:21:23,666 - helius_rate_limiter - INFO - Helius Rate Limiter initialized: 50.0 RPS
2025-06-22 15:21:23,666 - main - INFO - [SUCCESS] BULLETPROOF SEPARATION: Analysis and Position Monitoring functions initialized
2025-06-22 15:21:23,666 - main - INFO - [SUCCESS] PERMANENT TRACKING: 0 analyzed, 0 skipped
2025-06-22 15:21:23,678 - pumpportal_trader - DEBUG - Using MAINNET RPC: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-22 15:21:23,678 - pumpportal_trader - INFO - Loaded transaction settings: {}
2025-06-22 15:21:23,680 - pumpportal_trader - INFO - Loaded configured default wallet: TEST1 with public key: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-22 15:21:23,680 - pumpportal_trader - INFO - PumpPortal Trader initialized for mainnet pump.fun trading
2025-06-22 15:21:23,680 - pumpportal_trader - INFO - Mainnet API URL: https://pumpportal.fun/api/trade-local
2025-06-22 15:21:23,680 - pumpportal_trader - INFO - [WARNING] Note: PumpPortal only supports mainnet
2025-06-22 15:21:23,680 - pumpportal_trader - INFO - RPC URL: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-22 15:21:23,680 - pumpportal_trader - INFO - Helius rate limiter: 50.0 RPS
2025-06-22 15:21:23,681 - unified_trader - INFO - Unified Trader initialized for mainnet real trading with adaptive slippage support
2025-06-22 15:21:23,682 - main - INFO - CRITICAL DEBUG: SIMPLE_PUMP_AVAILABLE = True
2025-06-22 15:21:23,682 - main - INFO - CRITICAL DEBUG: _fast_analysis_function = <function init_simple_pump_analyzer.<locals>.analyze_new_signal_only at 0x0000028AE1F2FCE0>
2025-06-22 15:21:23,682 - main - INFO - CRITICAL DEBUG: _position_monitoring_function = <function init_simple_pump_analyzer.<locals>.monitor_position_only at 0x0000028AE1F2FD80>
2025-06-22 15:21:23,682 - main - INFO - [SUCCESS] Fast analysis function connected to bot controller (NEW SIGNALS ONLY)
2025-06-22 15:21:23,683 - main - INFO - [SUCCESS] Position monitoring function connected to bot controller (BOUGHT POSITIONS ONLY)
2025-06-22 15:21:23,683 - main - INFO - [AIM] MULTIPLIER FILTERING: ENABLED - Will reject tokens with 5x+ gains (allowing up to 5x multiplier)
2025-06-22 15:21:23,683 - main - INFO - Initializing DEX adapters and sessions
2025-06-22 15:21:23,683 - main - INFO - DEX sessions initialized successfully
2025-06-22 15:21:23,683 - main - INFO - LLM integration is not available in this configuration
2025-06-22 15:21:23,684 - main - INFO - Registered direct signal callback for faster signal proyescessing
2025-06-22 15:21:23,684 - main - INFO - LLM integration is not available in this configuration
2025-06-22 15:21:23,684 - main - INFO - Initializing bot controller WITHOUT signal processing...
2025-06-22 15:21:26,081 - main - INFO - [SUCCESS] Bot controller initialized successfully WITHOUT signal processing
2025-06-22 15:21:26,081 - main - INFO - Started cache cleanup task
2025-06-22 15:21:26,124 - production_monitor - INFO - Production monitoring started
2025-06-22 15:21:26,124 - main - INFO - [SUCCESS] Production monitoring started
2025-06-22 15:21:26,126 - error_tracker - INFO - [SUCCESS] Error tracking enabled
2025-06-22 15:21:26,126 - main - INFO - [SUCCESS] Error tracking enabled
2025-06-22 15:21:26,127 - main - INFO - [SUCCESS] Trading metrics collector enabled
2025-06-22 15:21:26,128 - main - INFO - Starting CLI interface...
2025-06-22 15:21:26,132 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:21:26,409 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:21:26,414 - production_monitor - INFO - Production monitor started
2025-06-22 15:21:26,522 - production_monitor - DEBUG - System healthy - CPU: 10.2%, Memory: 82.2%, Positions: 0
2025-06-22 15:21:28,964 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:21:29,114 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:21:31,742 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:21:31,918 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:21:36,283 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:21:36,442 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:21:43,773 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:21:43,933 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:21:47,296 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:21:47,551 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:21:48,619 - pumpportal_trader - DEBUG - Using MAINNET RPC: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-22 15:21:48,619 - pumpportal_trader - INFO - Loaded transaction settings: {}
2025-06-22 15:21:48,620 - pumpportal_trader - INFO - Loaded configured default wallet: TEST1 with public key: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-22 15:21:48,620 - pumpportal_trader - INFO - PumpPortal Trader initialized for mainnet pump.fun trading
2025-06-22 15:21:48,620 - pumpportal_trader - INFO - Mainnet API URL: https://pumpportal.fun/api/trade-local
2025-06-22 15:21:48,621 - pumpportal_trader - INFO - [WARNING] Note: PumpPortal only supports mainnet
2025-06-22 15:21:48,621 - pumpportal_trader - INFO - RPC URL: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-22 15:21:48,621 - pumpportal_trader - INFO - Helius rate limiter: 50.0 RPS
2025-06-22 15:21:48,621 - unified_trader - INFO - [SUCCESS] PumpPortal trader initialized for mainnet
2025-06-22 15:21:48,621 - unified_trader - INFO - ℹ️ Helius trader disabled - devnet support removed
2025-06-22 15:21:48,621 - unified_trader - INFO - [SUCCESS] PumpPortal trader initialized successfully (mainnet only)
2025-06-22 15:21:48,622 - pumpportal_trader - DEBUG - Using MAINNET RPC: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-22 15:21:48,622 - pumpportal_trader - INFO - [REFRESH] PumpPortal trader set to MAINNET mode
2025-06-22 15:21:48,622 - pumpportal_trader - INFO - [NETWORK] Updated RPC URL: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-22 15:21:48,623 - pumpportal_trader - INFO - 🔗 API URL: https://pumpportal.fun/api/trade-local
2025-06-22 15:21:48,623 - unified_trader - INFO - [LAUNCH] Confirmed MAINNET mode - using PumpPortal for pump.fun
2025-06-22 15:21:51,144 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:21:51,396 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:22:26,635 - production_monitor - DEBUG - System healthy - CPU: 17.0%, Memory: 82.3%, Positions: 0
2025-06-22 15:23:26,749 - production_monitor - DEBUG - System healthy - CPU: 0.0%, Memory: 82.6%, Positions: 0
2025-06-22 15:24:26,861 - production_monitor - DEBUG - System healthy - CPU: 1.0%, Memory: 82.2%, Positions: 0
2025-06-22 15:25:26,980 - production_monitor - DEBUG - System healthy - CPU: 7.5%, Memory: 81.6%, Positions: 0
2025-06-22 15:25:37,940 - main - INFO - [FAST] Analyzing token: 76oAhS14qJVB5z6UZp2b5MzVKzwZLJZymRs4B1sspump
2025-06-22 15:25:37,942 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.dexscreener.com:443
2025-06-22 15:25:37,943 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:25:37,944 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.coingecko.com:443
2025-06-22 15:25:38,023 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/76oAhS14qJVB5z6UZp2b5MzVKzwZLJZymRs4B1sspump HTTP/1.1" 200 None
2025-06-22 15:25:38,024 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:25:38,245 - urllib3.connectionpool - DEBUG - https://api.coingecko.com:443 "GET /api/v3/simple/price?ids=solana&vs_currencies=usd HTTP/1.1" 200 None
2025-06-22 15:25:38,245 - main - INFO - Fetched fresh SOL price: $133.32 (attempt 1)
2025-06-22 15:25:38,321 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:25:38,322 - main - DEBUG - Token supply success: 1,000,000,000
2025-06-22 15:25:38,324 - main - INFO - Fetched data - SOL price: $133.32, Supply: 1,000,000,000
2025-06-22 15:25:38,324 - main - INFO - DexScreener data available - Price: $2.552e-05, Volume 24h: $71,637
2025-06-22 15:25:38,324 - main - INFO - [SCAN] Looking for pump.fun pool for 76oAhS14qJVB5z6UZp2b5MzVKzwZLJZymRs4B1sspump
2025-06-22 15:25:38,325 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:25:38,756 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:25:38,758 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:25:39,182 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:25:39,183 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:25:39,558 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:25:39,558 - main - DEBUG - Pool balance success: 48.876231 SOL
2025-06-22 15:25:39,558 - main - INFO - [SUCCESS] Found pool address: 8gsXBXi25Q1Zp3cZ1M8Q9g3eoeiQLupaj3yqBuqpi5iK
2025-06-22 15:25:39,559 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:25:39,949 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:25:39,950 - main - DEBUG - Pool balance success: 48.771199 SOL
2025-06-22 15:25:39,950 - main - INFO - [MONEY] Pool SOL balance: 48.771199398
2025-06-22 15:25:39,950 - main - INFO - [CHART] Calculated liquidity: $12,679 (SOL: 48.771199398 * $133.32 * 1.95)
2025-06-22 15:25:39,950 - main - INFO - Found pool 8gsXBXi2... with 48.771199 SOL
2025-06-22 15:25:39,951 - main - INFO - Using calculated liquidity: $12,679 (x1.95)
2025-06-22 15:25:39,952 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:25:40,117 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:25:40,117 - main - DEBUG - Holders success: 20 holders
2025-06-22 15:25:40,118 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:25:40,379 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:25:40,380 - main - INFO - [FAST] Analysis completed in 2.44s
2025-06-22 15:25:40,380 - main - INFO - [SCAN] MULTIPLIER CHECK ENABLED: Checking 76oAhS14qJVB5z6UZp2b5MzVKzwZLJZymRs4B1sspump for high multipliers (3x+)
2025-06-22 15:25:40,381 - main - INFO - [CHART] PRICE CHANGES: 5m: 502.0%, 1h: 502.0%, 6h: 502.0%, 24h: 502.0% | Max: 502.0%
2025-06-22 15:25:40,381 - main - WARNING - 🚫 REJECTING HIGH MULTIPLIER TOKEN: 76oAhS14qJVB5z6UZp2b5MzVKzwZLJZymRs4B1sspump
2025-06-22 15:25:40,381 - main - WARNING -    Max price change: 502.0% (>= 400% = 5x)
2025-06-22 15:25:40,381 - main - WARNING -    REASON: Only buying coins with maximum 5x multiplier
2025-06-22 15:30:13,748 - unicode_test - INFO - [SUCCESS] Unicode-safe logging configured
2025-06-22 15:30:16,649 - unicode_test - INFO - [SUCCESS] Unicode-safe logging configured
2025-06-22 15:30:16,650 - main - INFO - Successfully configured Unicode-safe logging
2025-06-22 15:30:16,650 - main - INFO - Starting bot...
2025-06-22 15:30:16,651 - helius_rate_limiter - INFO - Helius Rate Limiter initialized: 50.0 RPS
2025-06-22 15:30:16,652 - main - INFO - [SUCCESS] BULLETPROOF SEPARATION: Analysis and Position Monitoring functions initialized
2025-06-22 15:30:16,652 - main - INFO - [SUCCESS] PERMANENT TRACKING: 0 analyzed, 0 skipped
2025-06-22 15:30:16,656 - pumpportal_trader - DEBUG - Using MAINNET RPC: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-22 15:30:16,656 - pumpportal_trader - INFO - Loaded transaction settings: {}
2025-06-22 15:30:16,657 - pumpportal_trader - INFO - Loaded configured default wallet: TEST1 with public key: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-22 15:30:16,657 - pumpportal_trader - INFO - PumpPortal Trader initialized for mainnet pump.fun trading
2025-06-22 15:30:16,658 - pumpportal_trader - INFO - Mainnet API URL: https://pumpportal.fun/api/trade-local
2025-06-22 15:30:16,658 - pumpportal_trader - INFO - [WARNING] Note: PumpPortal only supports mainnet
2025-06-22 15:30:16,658 - pumpportal_trader - INFO - RPC URL: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-22 15:30:16,658 - pumpportal_trader - INFO - Helius rate limiter: 50.0 RPS
2025-06-22 15:30:16,659 - unified_trader - INFO - Unified Trader initialized for mainnet real trading with adaptive slippage support
2025-06-22 15:30:16,660 - main - INFO - CRITICAL DEBUG: SIMPLE_PUMP_AVAILABLE = True
2025-06-22 15:30:16,660 - main - INFO - CRITICAL DEBUG: _fast_analysis_function = <function init_simple_pump_analyzer.<locals>.analyze_new_signal_only at 0x0000018329AFBCE0>
2025-06-22 15:30:16,660 - main - INFO - CRITICAL DEBUG: _position_monitoring_function = <function init_simple_pump_analyzer.<locals>.monitor_position_only at 0x0000018329AFBD80>
2025-06-22 15:30:16,660 - main - INFO - [SUCCESS] Fast analysis function connected to bot controller (NEW SIGNALS ONLY)
2025-06-22 15:30:16,660 - main - INFO - [SUCCESS] Position monitoring function connected to bot controller (BOUGHT POSITIONS ONLY)
2025-06-22 15:30:16,661 - main - INFO - [AIM] MULTIPLIER FILTERING: ENABLED - Will reject tokens with 5x+ gains (allowing up to 5x multiplier)
2025-06-22 15:30:16,661 - main - INFO - Initializing DEX adapters and sessions
2025-06-22 15:30:16,661 - main - INFO - DEX sessions initialized successfully
2025-06-22 15:30:16,661 - main - INFO - LLM integration is not available in this configuration
2025-06-22 15:30:16,661 - main - INFO - Registered direct signal callback for faster signal proyescessing
2025-06-22 15:30:16,661 - main - INFO - LLM integration is not available in this configuration
2025-06-22 15:30:16,662 - main - INFO - Initializing bot controller WITHOUT signal processing...
2025-06-22 15:30:18,839 - main - INFO - [SUCCESS] Bot controller initialized successfully WITHOUT signal processing
2025-06-22 15:30:18,839 - main - INFO - Started cache cleanup task
2025-06-22 15:30:18,853 - production_monitor - INFO - Production monitoring started
2025-06-22 15:30:18,853 - main - INFO - [SUCCESS] Production monitoring started
2025-06-22 15:30:18,855 - error_tracker - INFO - [SUCCESS] Error tracking enabled
2025-06-22 15:30:18,856 - main - INFO - [SUCCESS] Error tracking enabled
2025-06-22 15:30:18,857 - main - INFO - [SUCCESS] Trading metrics collector enabled
2025-06-22 15:30:18,858 - main - INFO - Starting CLI interface...
2025-06-22 15:30:18,865 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:30:19,286 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:30:19,291 - production_monitor - INFO - Production monitor started
2025-06-22 15:30:19,399 - production_monitor - DEBUG - System healthy - CPU: 13.9%, Memory: 85.0%, Positions: 0
2025-06-22 15:30:23,141 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:30:23,344 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:30:28,975 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:30:29,131 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:30:33,709 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:30:33,893 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:30:39,506 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:30:39,902 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:30:42,505 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:30:42,721 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:30:45,643 - pumpportal_trader - DEBUG - Using MAINNET RPC: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-22 15:30:45,643 - pumpportal_trader - INFO - Loaded transaction settings: {}
2025-06-22 15:30:45,644 - pumpportal_trader - INFO - Loaded configured default wallet: TEST1 with public key: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-22 15:30:45,644 - pumpportal_trader - INFO - PumpPortal Trader initialized for mainnet pump.fun trading
2025-06-22 15:30:45,644 - pumpportal_trader - INFO - Mainnet API URL: https://pumpportal.fun/api/trade-local
2025-06-22 15:30:45,644 - pumpportal_trader - INFO - [WARNING] Note: PumpPortal only supports mainnet
2025-06-22 15:30:45,644 - pumpportal_trader - INFO - RPC URL: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-22 15:30:45,644 - pumpportal_trader - INFO - Helius rate limiter: 50.0 RPS
2025-06-22 15:30:45,645 - unified_trader - INFO - [SUCCESS] PumpPortal trader initialized for mainnet
2025-06-22 15:30:45,645 - unified_trader - INFO - ℹ️ Helius trader disabled - devnet support removed
2025-06-22 15:30:45,645 - unified_trader - INFO - [SUCCESS] PumpPortal trader initialized successfully (mainnet only)
2025-06-22 15:30:45,646 - pumpportal_trader - DEBUG - Using MAINNET RPC: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-22 15:30:45,646 - pumpportal_trader - INFO - [REFRESH] PumpPortal trader set to MAINNET mode
2025-06-22 15:30:45,646 - pumpportal_trader - INFO - [NETWORK] Updated RPC URL: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-22 15:30:45,646 - pumpportal_trader - INFO - 🔗 API URL: https://pumpportal.fun/api/trade-local
2025-06-22 15:30:45,646 - unified_trader - INFO - [LAUNCH] Confirmed MAINNET mode - using PumpPortal for pump.fun
2025-06-22 15:30:48,158 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:30:48,393 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:31:19,515 - production_monitor - DEBUG - System healthy - CPU: 7.2%, Memory: 83.5%, Positions: 0
2025-06-22 15:32:19,629 - production_monitor - DEBUG - System healthy - CPU: 3.0%, Memory: 82.4%, Positions: 0
2025-06-22 15:32:44,065 - main - INFO - [FAST] Analyzing token: Ev5nopVUzjS1yzSSVARLMTVNFFHS8veis3Ljwr9fpump
2025-06-22 15:32:44,067 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.dexscreener.com:443
2025-06-22 15:32:44,067 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:32:44,068 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.coingecko.com:443
2025-06-22 15:32:44,277 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:32:44,278 - main - DEBUG - Token supply success: 999,082,574
2025-06-22 15:32:44,364 - urllib3.connectionpool - DEBUG - https://api.coingecko.com:443 "GET /api/v3/simple/price?ids=solana&vs_currencies=usd HTTP/1.1" 200 None
2025-06-22 15:32:44,364 - main - INFO - Fetched fresh SOL price: $132.77 (attempt 1)
2025-06-22 15:32:44,513 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/Ev5nopVUzjS1yzSSVARLMTVNFFHS8veis3Ljwr9fpump HTTP/1.1" 200 None
2025-06-22 15:32:44,513 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:32:44,514 - main - INFO - Fetched data - SOL price: $132.77, Supply: 999,082,574
2025-06-22 15:32:44,514 - main - INFO - DexScreener data available - Price: $1.524e-05, Volume 24h: $6,508
2025-06-22 15:32:44,514 - main - INFO - Using DexScreener liquidity: $12,034 (MC: $15,229)
2025-06-22 15:32:44,515 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:32:44,826 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:32:44,827 - main - DEBUG - Holders success: 20 holders
2025-06-22 15:32:44,827 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:32:45,307 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:32:45,307 - main - INFO - [FAST] Analysis completed in 1.24s
2025-06-22 15:32:45,308 - main - INFO - [SCAN] MULTIPLIER CHECK ENABLED: Checking Ev5nopVUzjS1yzSSVARLMTVNFFHS8veis3Ljwr9fpump for high multipliers (3x+)
2025-06-22 15:32:45,308 - main - INFO - [CHART] PRICE CHANGES: 5m: 9.6%, 1h: 2.9%, 6h: 110.0%, 24h: 110.0% | Max: 110.0%
2025-06-22 15:32:45,308 - main - INFO - [SUCCESS] 2X MULTIPLIER TOKEN: Ev5nopVUzjS1yzSSVARLMTVNFFHS8veis3Ljwr9fpump - 110.0% gain (good entry)
2025-06-22 15:32:45,310 - main - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for Ev5nopVUzjS1yzSSVARLMTVNFFHS8veis3Ljwr9fpump
2025-06-22 15:32:45,310 - main - WARNING - [ALERT] RUG PROTECTION TRIGGERED: High whale concentration 33.6% > 30.0%
2025-06-22 15:33:19,741 - production_monitor - DEBUG - System healthy - CPU: 26.5%, Memory: 80.8%, Positions: 0
2025-06-22 15:34:19,858 - production_monitor - DEBUG - System healthy - CPU: 4.1%, Memory: 80.8%, Positions: 0
2025-06-22 15:35:19,979 - production_monitor - DEBUG - System healthy - CPU: 9.0%, Memory: 80.9%, Positions: 0
2025-06-22 15:36:20,093 - production_monitor - DEBUG - System healthy - CPU: 12.1%, Memory: 80.3%, Positions: 0
2025-06-22 15:36:48,117 - main - INFO - [FAST] Analyzing token: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:36:48,122 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:36:48,290 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:36:48,292 - main - DEBUG - Token supply success: 999,984,309
2025-06-22 15:36:54,123 - main - DEBUG - DexScreener error (attempt 1): HTTPSConnectionPool(host='api.dexscreener.com', port=443): Read timed out. (read timeout=6)
2025-06-22 15:36:54,425 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (2): api.dexscreener.com:443
2025-06-22 15:36:54,799 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:36:54,799 - main - DEBUG - DexScreener success (attempt 2): 1 pairs found
2025-06-22 15:36:56,127 - main - ERROR - Failed to fetch SOL price (attempt 1): HTTPSConnectionPool(host='api.coingecko.com', port=443): Read timed out. (read timeout=8)
2025-06-22 15:36:56,630 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (2): api.coingecko.com:443
2025-06-22 15:36:56,947 - urllib3.connectionpool - DEBUG - https://api.coingecko.com:443 "GET /api/v3/simple/price?ids=solana&vs_currencies=usd HTTP/1.1" 200 None
2025-06-22 15:36:56,948 - main - INFO - Fetched fresh SOL price: $132.74 (attempt 2)
2025-06-22 15:36:56,949 - main - INFO - Fetched data - SOL price: $132.74, Supply: 999,984,309
2025-06-22 15:36:56,949 - main - INFO - DexScreener data available - Price: $1.482e-05, Volume 24h: $34,826
2025-06-22 15:36:56,949 - main - INFO - [SCAN] Looking for pump.fun pool for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:36:56,950 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:36:57,118 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:36:57,120 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:36:57,570 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:36:57,571 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:36:57,734 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:36:57,735 - main - DEBUG - Pool balance success: 29.959230 SOL
2025-06-22 15:36:57,735 - main - INFO - [SUCCESS] Found pool address: nbFdPuMJfCSABBKLfp5tCdCpai3DyBadGSzSt52oa8N
2025-06-22 15:36:57,737 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:36:57,905 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:36:57,906 - main - DEBUG - Pool balance success: 29.959230 SOL
2025-06-22 15:36:57,906 - main - INFO - [MONEY] Pool SOL balance: 29.959230464
2025-06-22 15:36:57,907 - main - INFO - [CHART] Calculated liquidity: $7,755 (SOL: 29.959230464 * $132.74 * 1.95)
2025-06-22 15:36:57,908 - main - INFO - Found pool nbFdPuMJ... with 29.959230 SOL
2025-06-22 15:36:57,908 - main - INFO - Using calculated liquidity: $7,755 (x1.95)
2025-06-22 15:36:57,910 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:36:58,165 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:36:58,166 - main - DEBUG - Holders success: 20 holders
2025-06-22 15:36:58,167 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:36:58,342 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:36:58,343 - main - INFO - [FAST] Analysis completed in 10.23s
2025-06-22 15:36:58,345 - main - INFO - [SCAN] MULTIPLIER CHECK ENABLED: Checking 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump for high multipliers (3x+)
2025-06-22 15:36:58,345 - main - INFO - [AIM] GMGN SIGNAL DETECTED: Allowing high multipliers for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:36:58,346 - main - INFO - [AIM] GMGN signals are exempt from multiplier restrictions
2025-06-22 15:36:58,351 - main - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:36:58,352 - main - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $7,755, MC: $14,827, Whale: 22.9%
2025-06-22 15:36:58,352 - main - INFO - [SUCCESS] Fast analysis result: Price=$0.00001482, MC=$14,827, Liq=$7,755
2025-06-22 15:36:58,353 - main - DEBUG - PERMANENT STATE: 1 total, 0 skipped
2025-06-22 15:36:58,353 - main - DEBUG - Permanent decision recorded for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:36:58,370 - pumpportal_trader - INFO - [MONEY] EXTERNAL FEES: Buy tip: 0.0001 SOL, Gas: 0.0001 SOL, Handling: 0.0000 SOL, Platform: 0.0000 SOL
2025-06-22 15:36:58,370 - pumpportal_trader - INFO - [MONEY] TOTAL EXTERNAL FEES: 0.0002 SOL
2025-06-22 15:36:58,372 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:36:58,618 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:36:58,619 - pumpportal_trader - DEBUG - Wallet balance: 0.053499 SOL
2025-06-22 15:36:58,619 - pumpportal_trader - INFO - [GREEN] REAL BUY: 0.0010 SOL of 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump (slippage: 21.0%) + 0.0002 SOL fees - Balance: 0.0535 SOL
2025-06-22 15:36:58,620 - pumpportal_trader - DEBUG - Priority fee: 0.******** SOL (100000 lamports)
2025-06-22 15:36:58,620 - pumpportal_trader - INFO - [SCAN] DETAILED PumpPortal payload:
2025-06-22 15:36:58,620 - pumpportal_trader - INFO -    action: buy
2025-06-22 15:36:58,621 - pumpportal_trader - INFO -    mint: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:36:58,621 - pumpportal_trader - INFO -    amount: 0.001 (original: 0.001)
2025-06-22 15:36:58,622 - pumpportal_trader - INFO -    denominatedInSol: true
2025-06-22 15:36:58,622 - pumpportal_trader - INFO -    slippage: 21
2025-06-22 15:36:58,622 - pumpportal_trader - INFO -    priorityFee: 0.0001 SOL
2025-06-22 15:36:58,623 - pumpportal_trader - INFO -    publicKey: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-22 15:36:58,623 - pumpportal_trader - INFO - [NETWORK] Using PumpPortal API: https://pumpportal.fun/api/trade-local (Network: MAINNET)
2025-06-22 15:36:58,625 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): pumpportal.fun:443
2025-06-22 15:36:59,529 - urllib3.connectionpool - DEBUG - https://pumpportal.fun:443 "POST /api/trade-local HTTP/1.1" 200 688
2025-06-22 15:36:59,529 - pumpportal_trader - INFO - [SIGNAL] PumpPortal API Response: 200
2025-06-22 15:36:59,532 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:36:59,767 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:36:59,768 - pumpportal_trader - INFO - Transaction sent successfully on attempt 1: 5PQSCjrwpkKAEojWrSTUeKZqFZhtE3sGBNUPuvWX3Jybu2vku1yqCW2Q4KyyFX4GG5T2G2cs7rnvvEMNHrhfgzdb
2025-06-22 15:36:59,769 - pumpportal_trader - INFO - [SUCCESS] Transaction sent successfully: 5PQSCjrwpkKAEojWrSTUeKZqFZhtE3sGBNUPuvWX3Jybu2vku1yqCW2Q4KyyFX4GG5T2G2cs7rnvvEMNHrhfgzdb
2025-06-22 15:36:59,770 - pumpportal_trader - INFO - [SCAN] CRITICAL FIX: Waiting for blockchain verification before confirming success...
2025-06-22 15:36:59,772 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:37:00,949 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:37:02,963 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:37:03,146 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:37:03,147 - pumpportal_trader - INFO - [SUCCESS] Transaction VERIFIED successful: 5PQSCjrwpkKAEojWrSTUeKZqFZhtE3sGBNUPuvWX3Jybu2vku1yqCW2Q4KyyFX4GG5T2G2cs7rnvvEMNHrhfgzdb
2025-06-22 15:37:03,148 - pumpportal_trader - INFO - [SUCCESS] VERIFIED: Transaction confirmed on blockchain: 5PQSCjrwpkKAEojWrSTUeKZqFZhtE3sGBNUPuvWX3Jybu2vku1yqCW2Q4KyyFX4GG5T2G2cs7rnvvEMNHrhfgzdb
2025-06-22 15:37:03,149 - pumpportal_trader - INFO - [SUCCESS] Buy successful: 5PQSCjrwpkKAEojWrSTUeKZqFZhtE3sGBNUPuvWX3Jybu2vku1yqCW2Q4KyyFX4GG5T2G2cs7rnvvEMNHrhfgzdb (Total cost: 0.0012 SOL)
2025-06-22 15:37:03,579 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:37:03,769 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:37:04,500 - main - INFO - [FAST] Analyzing token: 76oAhS14qJVB5z6UZp2b5MzVKzwZLJZymRs4B1sspump
2025-06-22 15:37:04,502 - main - DEBUG - Using cached SOL price: $132.74 (age: 16.4s)
2025-06-22 15:37:04,504 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:37:04,744 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:37:04,745 - main - DEBUG - Token supply success: 999,506,289
2025-06-22 15:37:04,843 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/76oAhS14qJVB5z6UZp2b5MzVKzwZLJZymRs4B1sspump HTTP/1.1" 200 None
2025-06-22 15:37:04,844 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:04,845 - main - INFO - Fetched data - SOL price: $132.74, Supply: 999,506,289
2025-06-22 15:37:04,846 - main - INFO - DexScreener data available - Price: $4.006e-05, Volume 24h: $150,714
2025-06-22 15:37:04,847 - main - INFO - [SCAN] Looking for pump.fun pool for 76oAhS14qJVB5z6UZp2b5MzVKzwZLJZymRs4B1sspump
2025-06-22 15:37:04,849 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:37:05,285 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:37:05,286 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:37:05,455 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:37:05,457 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:37:05,642 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:37:05,643 - main - DEBUG - Pool balance success: 67.392707 SOL
2025-06-22 15:37:05,643 - main - INFO - [SUCCESS] Found pool address: 8gsXBXi25Q1Zp3cZ1M8Q9g3eoeiQLupaj3yqBuqpi5iK
2025-06-22 15:37:05,645 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:37:05,851 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:37:05,852 - main - DEBUG - Pool balance success: 67.492707 SOL
2025-06-22 15:37:05,852 - main - INFO - [MONEY] Pool SOL balance: 67.*************-06-22 15:37:05,853 - main - INFO - [CHART] Calculated liquidity: $17,470 (SOL: 67.49270693 * $132.74 * 1.95)
2025-06-22 15:37:05,853 - main - INFO - Found pool 8gsXBXi2... with 67.492707 SOL
2025-06-22 15:37:05,854 - main - INFO - Using calculated liquidity: $17,470 (x1.95)
2025-06-22 15:37:05,856 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:37:06,104 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:37:06,105 - main - DEBUG - Holders success: 20 holders
2025-06-22 15:37:06,107 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:37:06,335 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:37:06,336 - main - INFO - [FAST] Analysis completed in 1.84s
2025-06-22 15:37:06,336 - main - INFO - [SCAN] MULTIPLIER CHECK ENABLED: Checking 76oAhS14qJVB5z6UZp2b5MzVKzwZLJZymRs4B1sspump for high multipliers (3x+)
2025-06-22 15:37:06,336 - main - INFO - [AIM] GMGN SIGNAL DETECTED: Allowing high multipliers for 76oAhS14qJVB5z6UZp2b5MzVKzwZLJZymRs4B1sspump
2025-06-22 15:37:06,336 - main - INFO - [AIM] GMGN signals are exempt from multiplier restrictions
2025-06-22 15:37:06,338 - main - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 76oAhS14qJVB5z6UZp2b5MzVKzwZLJZymRs4B1sspump
2025-06-22 15:37:06,338 - main - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $17,470, MC: $40,064, Whale: 26.0%
2025-06-22 15:37:06,338 - main - INFO - [SUCCESS] Fast analysis result: Price=$0.00004006, MC=$40,064, Liq=$17,470
2025-06-22 15:37:06,338 - main - DEBUG - PERMANENT STATE: 2 total, 0 skipped
2025-06-22 15:37:06,339 - main - DEBUG - Permanent decision recorded for 76oAhS14qJVB5z6UZp2b5MzVKzwZLJZymRs4B1sspump
2025-06-22 15:37:10,956 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:10,956 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:11,005 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:11,005 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:11,006 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001482, Liquidity: $0
2025-06-22 15:37:12,310 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:12,310 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:12,332 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:12,333 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:12,333 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001482, Liquidity: $0
2025-06-22 15:37:13,645 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:13,645 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:13,673 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:13,674 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:13,674 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001482, Liquidity: $0
2025-06-22 15:37:14,984 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:14,984 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:15,004 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:15,005 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:15,005 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001482, Liquidity: $0
2025-06-22 15:37:16,323 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:16,323 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:16,351 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:16,351 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:16,351 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001482, Liquidity: $0
2025-06-22 15:37:17,673 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:17,673 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:17,710 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:17,711 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:17,711 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001482, Liquidity: $0
2025-06-22 15:37:19,018 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:19,018 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:19,045 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:19,045 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:19,045 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001482, Liquidity: $0
2025-06-22 15:37:20,220 - production_monitor - DEBUG - System healthy - CPU: 2.1%, Memory: 80.2%, Positions: 0
2025-06-22 15:37:20,357 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:20,357 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:20,386 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:20,387 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:20,387 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001482, Liquidity: $0
2025-06-22 15:37:21,707 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:21,708 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:21,730 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:21,730 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:21,731 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001482, Liquidity: $0
2025-06-22 15:37:23,044 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:23,044 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:23,067 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:23,068 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:23,068 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001482, Liquidity: $0
2025-06-22 15:37:24,382 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:24,382 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:24,404 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:24,404 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:24,404 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001482, Liquidity: $0
2025-06-22 15:37:25,713 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:25,713 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:26,624 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:26,625 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:26,625 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001379, Liquidity: $0
2025-06-22 15:37:27,934 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:27,934 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:27,955 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:27,955 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:27,955 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001379, Liquidity: $0
2025-06-22 15:37:29,271 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:29,271 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:29,297 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:29,298 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:29,298 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001379, Liquidity: $0
2025-06-22 15:37:29,354 - main - INFO - [FAST] Analyzing token: FHL95GDohBorPH5UNL5U1HPiTbgoYzchR8hfrdmtpump
2025-06-22 15:37:29,355 - main - DEBUG - Using cached SOL price: $132.74 (age: 41.2s)
2025-06-22 15:37:29,356 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:37:29,561 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:37:29,562 - main - DEBUG - Token supply success: 1,000,000,000
2025-06-22 15:37:29,759 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/FHL95GDohBorPH5UNL5U1HPiTbgoYzchR8hfrdmtpump HTTP/1.1" 200 None
2025-06-22 15:37:29,759 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:29,760 - main - INFO - Fetched data - SOL price: $132.74, Supply: 1,000,000,000
2025-06-22 15:37:29,760 - main - INFO - DexScreener data available - Price: $8.395e-06, Volume 24h: $13,820
2025-06-22 15:37:29,760 - main - INFO - [SCAN] Looking for pump.fun pool for FHL95GDohBorPH5UNL5U1HPiTbgoYzchR8hfrdmtpump
2025-06-22 15:37:29,761 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:37:29,926 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:37:29,927 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:37:30,104 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:37:30,107 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:37:30,341 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:37:30,342 - main - DEBUG - Pool balance success: 27.030637 SOL
2025-06-22 15:37:30,342 - main - INFO - [SUCCESS] Found pool address: GcFb8VDP7NXWG61VguyR4MA9J1ewmRra62mbSm576Mt4
2025-06-22 15:37:30,344 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:37:30,529 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:37:30,530 - main - DEBUG - Pool balance success: 27.030637 SOL
2025-06-22 15:37:30,531 - main - INFO - [MONEY] Pool SOL balance: 27.03063738
2025-06-22 15:37:30,531 - main - INFO - [CHART] Calculated liquidity: $6,997 (SOL: 27.03063738 * $132.74 * 1.95)
2025-06-22 15:37:30,532 - main - INFO - Found pool GcFb8VDP... with 27.030637 SOL
2025-06-22 15:37:30,532 - main - INFO - Using calculated liquidity: $6,997 (x1.95)
2025-06-22 15:37:30,534 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:37:30,927 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:37:30,928 - main - DEBUG - Holders success: 20 holders
2025-06-22 15:37:30,930 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:37:31,098 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:37:31,100 - main - INFO - [FAST] Analysis completed in 1.75s
2025-06-22 15:37:31,101 - main - INFO - [SCAN] MULTIPLIER CHECK ENABLED: Checking FHL95GDohBorPH5UNL5U1HPiTbgoYzchR8hfrdmtpump for high multipliers (3x+)
2025-06-22 15:37:31,101 - main - INFO - [AIM] GMGN SIGNAL DETECTED: Allowing high multipliers for FHL95GDohBorPH5UNL5U1HPiTbgoYzchR8hfrdmtpump
2025-06-22 15:37:31,102 - main - INFO - [AIM] GMGN signals are exempt from multiplier restrictions
2025-06-22 15:37:31,106 - main - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for FHL95GDohBorPH5UNL5U1HPiTbgoYzchR8hfrdmtpump
2025-06-22 15:37:31,107 - main - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $6,997 < $7,000
2025-06-22 15:37:31,113 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:31,113 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:31,141 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:31,142 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:31,142 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001379, Liquidity: $0
2025-06-22 15:37:32,452 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:32,453 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:32,476 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:32,477 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:32,477 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001379, Liquidity: $0
2025-06-22 15:37:33,791 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:33,791 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:33,813 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:33,814 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:33,814 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001379, Liquidity: $0
2025-06-22 15:37:35,127 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:35,127 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:35,150 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:35,151 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:35,151 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001379, Liquidity: $0
2025-06-22 15:37:36,460 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:36,460 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:36,483 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:36,483 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:36,483 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001379, Liquidity: $0
2025-06-22 15:37:37,793 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:37,794 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:37,826 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:37,827 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:37,827 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001379, Liquidity: $0
2025-06-22 15:37:39,133 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:39,133 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:39,164 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:39,165 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:39,165 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001379, Liquidity: $0
2025-06-22 15:37:40,482 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:40,482 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:40,503 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:40,504 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:40,504 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001379, Liquidity: $0
2025-06-22 15:37:41,816 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:41,817 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:41,839 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:41,839 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:41,840 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001379, Liquidity: $0
2025-06-22 15:37:43,150 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:43,150 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:43,178 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:43,178 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:43,178 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001379, Liquidity: $0
2025-06-22 15:37:44,486 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:44,486 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:44,512 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:44,513 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:44,513 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001379, Liquidity: $0
2025-06-22 15:37:45,824 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:45,825 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:45,848 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:45,849 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:45,850 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001379, Liquidity: $0
2025-06-22 15:37:47,161 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:47,161 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:47,190 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:47,191 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:47,191 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001379, Liquidity: $0
2025-06-22 15:37:48,510 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:48,510 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:48,543 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:48,544 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:48,544 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001379, Liquidity: $0
2025-06-22 15:37:49,860 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:49,860 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:49,884 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:49,884 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:49,885 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001379, Liquidity: $0
2025-06-22 15:37:51,198 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:51,198 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:51,227 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:51,227 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:51,228 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001379, Liquidity: $0
2025-06-22 15:37:52,549 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:52,549 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:52,571 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:52,572 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:52,572 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001379, Liquidity: $0
2025-06-22 15:37:53,886 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:53,886 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:53,909 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:53,910 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:53,910 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001379, Liquidity: $0
2025-06-22 15:37:55,222 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:55,222 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:55,248 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:55,249 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:55,249 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001379, Liquidity: $0
2025-06-22 15:37:56,560 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:56,560 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:56,593 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:56,594 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:56,594 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001379, Liquidity: $0
2025-06-22 15:37:57,912 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:57,912 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:58,255 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:58,256 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:58,256 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001194, Liquidity: $0
2025-06-22 15:37:59,570 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:59,570 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:37:59,610 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:37:59,611 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:37:59,611 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001194, Liquidity: $0
2025-06-22 15:38:00,923 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:00,923 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:00,944 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:00,944 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:00,944 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001194, Liquidity: $0
2025-06-22 15:38:02,257 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:02,258 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:02,280 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:02,281 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:02,282 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001194, Liquidity: $0
2025-06-22 15:38:03,592 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:03,593 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:03,623 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:03,623 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:03,623 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001194, Liquidity: $0
2025-06-22 15:38:04,940 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:04,940 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:04,964 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:04,965 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:04,965 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001194, Liquidity: $0
2025-06-22 15:38:06,278 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:06,278 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:06,302 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:06,302 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:06,303 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001194, Liquidity: $0
2025-06-22 15:38:07,615 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:07,615 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:07,637 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:07,637 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:07,638 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001194, Liquidity: $0
2025-06-22 15:38:08,950 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:08,950 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:08,974 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:08,974 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:08,975 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001194, Liquidity: $0
2025-06-22 15:38:10,286 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:10,286 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:10,308 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:10,308 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:10,308 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001194, Liquidity: $0
2025-06-22 15:38:11,622 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:11,622 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:11,651 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:11,652 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:11,652 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001194, Liquidity: $0
2025-06-22 15:38:12,967 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:12,967 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:12,988 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:12,989 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:12,989 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001194, Liquidity: $0
2025-06-22 15:38:14,308 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:14,308 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:14,338 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:14,339 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:14,339 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001194, Liquidity: $0
2025-06-22 15:38:15,656 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:15,656 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:15,676 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:15,676 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:15,676 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001194, Liquidity: $0
2025-06-22 15:38:16,996 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:16,996 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:17,021 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:17,021 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:17,021 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001194, Liquidity: $0
2025-06-22 15:38:18,332 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:18,333 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:18,363 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:18,364 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:18,365 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001194, Liquidity: $0
2025-06-22 15:38:19,686 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:19,687 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:19,709 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:19,710 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:19,710 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001194, Liquidity: $0
2025-06-22 15:38:20,341 - production_monitor - DEBUG - System healthy - CPU: 10.4%, Memory: 80.3%, Positions: 0
2025-06-22 15:38:21,026 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:21,026 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:21,048 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:21,049 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:21,049 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001194, Liquidity: $0
2025-06-22 15:38:22,362 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:22,363 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:22,388 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:22,389 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:22,389 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001194, Liquidity: $0
2025-06-22 15:38:23,707 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:23,707 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:23,728 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:23,729 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:23,729 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001194, Liquidity: $0
2025-06-22 15:38:25,043 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:25,043 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:25,064 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:25,065 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:25,065 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001194, Liquidity: $0
2025-06-22 15:38:26,375 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:26,375 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:26,400 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:26,400 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:26,400 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001194, Liquidity: $0
2025-06-22 15:38:27,715 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:27,715 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:27,739 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:27,740 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:27,740 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001194, Liquidity: $0
2025-06-22 15:38:29,049 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:29,049 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:29,071 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:29,072 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:29,072 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001194, Liquidity: $0
2025-06-22 15:38:30,385 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:30,385 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:30,713 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:30,714 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:30,714 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001310, Liquidity: $0
2025-06-22 15:38:32,024 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:32,024 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:32,050 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:32,050 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:32,050 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001310, Liquidity: $0
2025-06-22 15:38:33,362 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:33,362 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:33,386 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:33,387 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:33,387 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001310, Liquidity: $0
2025-06-22 15:38:34,695 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:34,695 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:34,717 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:34,718 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:34,719 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001310, Liquidity: $0
2025-06-22 15:38:36,025 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:36,025 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:36,054 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:36,055 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:36,056 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001310, Liquidity: $0
2025-06-22 15:38:37,368 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:37,368 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:37,391 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:37,391 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:37,392 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001310, Liquidity: $0
2025-06-22 15:38:38,705 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:38,705 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:38,727 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:38,728 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:38,728 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001310, Liquidity: $0
2025-06-22 15:38:40,042 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:40,042 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:40,063 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:40,064 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:40,065 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001310, Liquidity: $0
2025-06-22 15:38:41,376 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:41,376 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:41,397 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:41,398 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:41,398 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001310, Liquidity: $0
2025-06-22 15:38:42,719 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:42,719 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:42,742 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:42,743 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:42,744 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001310, Liquidity: $0
2025-06-22 15:38:44,055 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:44,055 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:44,077 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:44,078 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:44,078 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001310, Liquidity: $0
2025-06-22 15:38:45,398 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:45,398 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:45,420 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:45,421 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:45,422 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001310, Liquidity: $0
2025-06-22 15:38:46,738 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:46,738 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:46,761 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:46,761 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:46,762 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001310, Liquidity: $0
2025-06-22 15:38:48,077 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:48,077 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:48,103 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:48,103 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:48,103 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001310, Liquidity: $0
2025-06-22 15:38:49,415 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:49,416 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:49,438 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:49,439 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:49,439 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001310, Liquidity: $0
2025-06-22 15:38:50,754 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:50,754 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:50,782 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:50,782 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:50,782 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001310, Liquidity: $0
2025-06-22 15:38:52,093 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:52,093 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:52,117 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:52,118 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:52,118 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001310, Liquidity: $0
2025-06-22 15:38:53,434 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:53,434 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:53,472 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:53,473 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:53,473 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001310, Liquidity: $0
2025-06-22 15:38:54,784 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:54,784 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:54,805 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:54,805 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:54,805 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001310, Liquidity: $0
2025-06-22 15:38:56,117 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:56,117 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:56,142 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:56,142 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:56,142 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001310, Liquidity: $0
2025-06-22 15:38:57,455 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:57,455 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:57,477 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:57,477 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:57,478 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001310, Liquidity: $0
2025-06-22 15:38:58,795 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:58,795 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:38:58,819 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:38:58,820 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:38:58,820 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001310, Liquidity: $0
2025-06-22 15:39:00,141 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:00,141 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:00,164 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:00,164 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:00,164 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001310, Liquidity: $0
2025-06-22 15:39:01,478 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:01,478 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:01,814 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:01,815 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:01,815 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001267, Liquidity: $0
2025-06-22 15:39:03,125 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:03,125 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:03,155 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:03,156 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:03,156 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001267, Liquidity: $0
2025-06-22 15:39:03,262 - main - INFO - [FAST] Analyzing token: HY8Njwr1Vent7vTG5ur7gdVbKHANRC3G5jmipXdvpump
2025-06-22 15:39:03,264 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:39:03,503 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:39:03,504 - main - DEBUG - Token supply success: 999,999,999
2025-06-22 15:39:03,506 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/HY8Njwr1Vent7vTG5ur7gdVbKHANRC3G5jmipXdvpump HTTP/1.1" 200 None
2025-06-22 15:39:03,507 - main - DEBUG - DexScreener success (attempt 1): 2 pairs found
2025-06-22 15:39:03,555 - urllib3.connectionpool - DEBUG - https://api.coingecko.com:443 "GET /api/v3/simple/price?ids=solana&vs_currencies=usd HTTP/1.1" 200 None
2025-06-22 15:39:03,556 - main - INFO - Fetched fresh SOL price: $132.65 (attempt 1)
2025-06-22 15:39:03,557 - main - INFO - Fetched data - SOL price: $132.65, Supply: 999,999,999
2025-06-22 15:39:03,558 - main - INFO - DexScreener data available - Price: $0.001207, Volume 24h: $349,950
2025-06-22 15:39:03,558 - main - INFO - Using DexScreener liquidity: $107,809 (MC: $1,207,911)
2025-06-22 15:39:03,560 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:39:03,879 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:39:03,880 - main - DEBUG - Holders success: 20 holders
2025-06-22 15:39:03,882 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:39:04,413 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:39:04,414 - main - INFO - [FAST] Analysis completed in 1.15s
2025-06-22 15:39:04,414 - main - INFO - [SCAN] MULTIPLIER CHECK ENABLED: Checking HY8Njwr1Vent7vTG5ur7gdVbKHANRC3G5jmipXdvpump for high multipliers (3x+)
2025-06-22 15:39:04,415 - main - INFO - [CHART] PRICE CHANGES: 5m: 5.4%, 1h: 169.0%, 6h: 2056.0%, 24h: 2056.0% | Max: 2056.0%
2025-06-22 15:39:04,415 - main - WARNING - 🚫 REJECTING HIGH MULTIPLIER TOKEN: HY8Njwr1Vent7vTG5ur7gdVbKHANRC3G5jmipXdvpump
2025-06-22 15:39:04,415 - main - WARNING -    Max price change: 2056.0% (>= 400% = 5x)
2025-06-22 15:39:04,415 - main - WARNING -    REASON: Only buying coins with maximum 5x multiplier
2025-06-22 15:39:04,415 - main - WARNING -    NOTE: GMGN signals are exempt from this restriction
2025-06-22 15:39:04,472 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:04,472 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:04,494 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:04,495 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:04,495 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001267, Liquidity: $0
2025-06-22 15:39:05,798 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:05,798 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:05,834 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:05,835 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:05,836 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001267, Liquidity: $0
2025-06-22 15:39:07,140 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:07,140 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:07,160 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:07,161 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:07,161 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001267, Liquidity: $0
2025-06-22 15:39:08,473 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:08,473 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:08,502 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:08,502 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:08,502 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001267, Liquidity: $0
2025-06-22 15:39:09,814 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:09,814 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:09,836 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:09,837 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:09,837 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001267, Liquidity: $0
2025-06-22 15:39:11,148 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:11,148 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:11,172 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:11,172 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:11,172 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001267, Liquidity: $0
2025-06-22 15:39:12,479 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:12,479 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:12,510 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:12,511 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:12,512 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001267, Liquidity: $0
2025-06-22 15:39:13,823 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:13,823 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:13,845 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:13,846 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:13,846 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001267, Liquidity: $0
2025-06-22 15:39:15,159 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:15,159 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:15,180 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:15,180 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:15,180 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001267, Liquidity: $0
2025-06-22 15:39:16,494 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:16,495 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:16,518 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:16,519 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:16,519 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001267, Liquidity: $0
2025-06-22 15:39:17,840 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:17,840 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:17,864 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:17,864 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:17,864 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001267, Liquidity: $0
2025-06-22 15:39:19,175 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:19,175 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:19,206 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:19,207 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:19,207 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001267, Liquidity: $0
2025-06-22 15:39:20,459 - production_monitor - DEBUG - System healthy - CPU: 12.0%, Memory: 78.3%, Positions: 0
2025-06-22 15:39:20,526 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:20,526 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:20,547 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:20,548 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:20,548 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001267, Liquidity: $0
2025-06-22 15:39:21,866 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:21,866 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:21,892 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:21,893 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:21,893 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001267, Liquidity: $0
2025-06-22 15:39:23,197 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:23,197 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:23,221 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:23,221 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:23,221 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001267, Liquidity: $0
2025-06-22 15:39:24,531 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:24,531 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:24,562 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:24,562 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:24,562 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001267, Liquidity: $0
2025-06-22 15:39:25,880 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:25,881 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:25,902 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:25,903 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:25,903 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001267, Liquidity: $0
2025-06-22 15:39:27,214 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:27,214 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:27,245 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:27,246 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:27,246 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001267, Liquidity: $0
2025-06-22 15:39:28,567 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:28,567 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:28,589 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:28,590 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:28,590 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001267, Liquidity: $0
2025-06-22 15:39:29,899 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:29,899 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:29,925 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:29,925 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:29,926 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001267, Liquidity: $0
2025-06-22 15:39:31,239 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:31,239 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:31,268 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:31,269 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:31,269 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001267, Liquidity: $0
2025-06-22 15:39:32,580 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:32,582 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:32,891 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:32,892 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:32,893 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001139, Liquidity: $0
2025-06-22 15:39:34,215 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:34,215 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:34,239 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:34,240 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:34,240 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001139, Liquidity: $0
2025-06-22 15:39:35,549 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:35,549 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:35,575 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:35,576 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:35,576 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001139, Liquidity: $0
2025-06-22 15:39:36,896 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:36,896 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:36,923 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:36,923 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:36,923 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001139, Liquidity: $0
2025-06-22 15:39:38,236 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:38,236 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:38,261 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:38,261 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:38,261 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001139, Liquidity: $0
2025-06-22 15:39:39,577 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:39,577 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:39,599 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:39,600 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:39,600 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001139, Liquidity: $0
2025-06-22 15:39:40,912 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:40,912 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:40,935 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:40,936 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:40,936 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001139, Liquidity: $0
2025-06-22 15:39:42,247 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:42,248 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:42,272 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:42,272 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:42,273 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001139, Liquidity: $0
2025-06-22 15:39:43,588 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:43,589 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:43,613 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:43,614 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:43,614 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001139, Liquidity: $0
2025-06-22 15:39:44,930 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:44,930 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:44,951 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:44,952 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:44,952 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001139, Liquidity: $0
2025-06-22 15:39:46,264 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:46,264 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:46,289 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:46,289 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:46,289 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001139, Liquidity: $0
2025-06-22 15:39:47,595 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:47,596 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:47,616 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:47,617 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:47,618 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001139, Liquidity: $0
2025-06-22 15:39:48,932 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:48,933 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:48,967 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:48,968 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:48,968 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001139, Liquidity: $0
2025-06-22 15:39:50,284 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:50,285 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:50,308 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:50,309 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:50,309 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001139, Liquidity: $0
2025-06-22 15:39:51,621 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:51,621 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:51,644 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:51,645 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:51,645 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001139, Liquidity: $0
2025-06-22 15:39:52,949 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:52,950 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:52,976 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:52,977 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:52,977 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001139, Liquidity: $0
2025-06-22 15:39:54,290 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:54,290 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:54,313 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:54,314 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:54,314 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001139, Liquidity: $0
2025-06-22 15:39:55,628 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:55,628 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:55,658 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:55,658 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:55,658 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001139, Liquidity: $0
2025-06-22 15:39:56,969 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:56,970 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:56,994 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:56,994 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:56,995 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001139, Liquidity: $0
2025-06-22 15:39:58,306 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:58,306 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:58,327 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:58,328 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:58,328 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001139, Liquidity: $0
2025-06-22 15:39:59,643 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:59,643 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:39:59,663 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:39:59,664 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:39:59,664 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001139, Liquidity: $0
2025-06-22 15:40:00,974 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:40:00,974 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:40:01,014 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:40:01,014 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:40:01,014 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001139, Liquidity: $0
2025-06-22 15:40:02,324 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:40:02,325 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:40:02,359 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:40:02,359 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:40:02,359 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00001139, Liquidity: $0
2025-06-22 15:40:03,678 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:40:03,678 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:40:03,989 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:40:03,989 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:40:03,990 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00000945, Liquidity: $0
2025-06-22 15:40:03,992 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:40:03,992 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:40:04,019 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:40:04,020 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:40:04,020 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00000945, Liquidity: $0
2025-06-22 15:40:04,024 - pumpportal_trader - INFO - [MONEY] SELL FEES: Gas: 0.0001 SOL, Handling: 1.0%, Platform: 1.0%
2025-06-22 15:40:04,025 - pumpportal_trader - DEBUG - [SCAN] Checking token balance for 9VmPUjL5... in wallet 75eUxZsu...
2025-06-22 15:40:04,025 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:40:04,259 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:40:04,261 - pumpportal_trader - DEBUG - [SCAN] Found 1 token accounts for 9VmPUjL5...
2025-06-22 15:40:04,261 - pumpportal_trader - INFO - [SUCCESS] Token balance found: 9689.286680 tokens (raw: **********, decimals: 6)
2025-06-22 15:40:04,262 - pumpportal_trader - INFO - [SCAN] PRE-SELL TOKEN BALANCE: 9689.286680 tokens for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:40:04,262 - pumpportal_trader - INFO - [SCAN] Requested sell amount: 67.476383 tokens
2025-06-22 15:40:04,263 - pumpportal_trader - INFO - [RED] REAL SELL: 0.696% (67.4764/9689.2867 tokens) of 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:40:04,264 - pumpportal_trader - DEBUG - Priority fee: 0.******** SOL (100000 lamports)
2025-06-22 15:40:04,264 - pumpportal_trader - INFO - [FIX] CRITICAL FIX: Keeping percentage as string: '0.696%' (PumpPortal requires string format)
2025-06-22 15:40:04,265 - pumpportal_trader - INFO - [SCAN] DETAILED PumpPortal payload:
2025-06-22 15:40:04,265 - pumpportal_trader - INFO -    action: sell
2025-06-22 15:40:04,266 - pumpportal_trader - INFO -    mint: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:40:04,266 - pumpportal_trader - INFO -    amount: 0.696% (original: 0.696%)
2025-06-22 15:40:04,267 - pumpportal_trader - INFO -    denominatedInSol: false
2025-06-22 15:40:04,268 - pumpportal_trader - INFO -    slippage: 29
2025-06-22 15:40:04,268 - pumpportal_trader - INFO -    priorityFee: 0.0001 SOL
2025-06-22 15:40:04,269 - pumpportal_trader - INFO -    publicKey: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-22 15:40:04,269 - pumpportal_trader - INFO - [NETWORK] Using PumpPortal API: https://pumpportal.fun/api/trade-local (Network: MAINNET)
2025-06-22 15:40:04,272 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): pumpportal.fun:443
2025-06-22 15:40:05,018 - urllib3.connectionpool - DEBUG - https://pumpportal.fun:443 "POST /api/trade-local HTTP/1.1" 200 696
2025-06-22 15:40:05,018 - pumpportal_trader - INFO - [SIGNAL] PumpPortal API Response: 200
2025-06-22 15:40:05,020 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:40:05,185 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:40:05,186 - pumpportal_trader - INFO - Transaction sent successfully on attempt 1: BAMS5pV4XvWsGnSjYXSmUSwCthLuVoDwyp6Cz3QiC7MrcxgvHfcrzNSCse6FZf3LEV6AFUBhRieoAwUMx5A3jgj
2025-06-22 15:40:05,187 - pumpportal_trader - INFO - [SUCCESS] Transaction sent successfully: BAMS5pV4XvWsGnSjYXSmUSwCthLuVoDwyp6Cz3QiC7MrcxgvHfcrzNSCse6FZf3LEV6AFUBhRieoAwUMx5A3jgj
2025-06-22 15:40:05,187 - pumpportal_trader - INFO - [SCAN] CRITICAL FIX: Waiting for blockchain verification before confirming success...
2025-06-22 15:40:05,188 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:40:05,736 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:40:07,749 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:40:07,960 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:40:07,961 - pumpportal_trader - INFO - [SUCCESS] Transaction VERIFIED successful: BAMS5pV4XvWsGnSjYXSmUSwCthLuVoDwyp6Cz3QiC7MrcxgvHfcrzNSCse6FZf3LEV6AFUBhRieoAwUMx5A3jgj
2025-06-22 15:40:07,961 - pumpportal_trader - INFO - [SUCCESS] VERIFIED: Transaction confirmed on blockchain: BAMS5pV4XvWsGnSjYXSmUSwCthLuVoDwyp6Cz3QiC7MrcxgvHfcrzNSCse6FZf3LEV6AFUBhRieoAwUMx5A3jgj
2025-06-22 15:40:07,962 - pumpportal_trader - INFO - [SUCCESS] Sell successful: BAMS5pV4XvWsGnSjYXSmUSwCthLuVoDwyp6Cz3QiC7MrcxgvHfcrzNSCse6FZf3LEV6AFUBhRieoAwUMx5A3jgj (Gas cost: 0.0001 SOL)
2025-06-22 15:40:07,962 - pumpportal_trader - INFO - [SCAN] CRITICAL DEBUG: Transaction verified on blockchain: True
2025-06-22 15:40:07,962 - pumpportal_trader - INFO - 🔗 Solscan link: https://solscan.io/tx/BAMS5pV4XvWsGnSjYXSmUSwCthLuVoDwyp6Cz3QiC7MrcxgvHfcrzNSCse6FZf3LEV6AFUBhRieoAwUMx5A3jgj
2025-06-22 15:40:07,963 - pumpportal_trader - DEBUG - [SCAN] Checking token balance for 9VmPUjL5... in wallet 75eUxZsu...
2025-06-22 15:40:07,963 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:40:08,385 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:40:08,386 - pumpportal_trader - DEBUG - [SCAN] Found 1 token accounts for 9VmPUjL5...
2025-06-22 15:40:08,386 - pumpportal_trader - INFO - [SUCCESS] Token balance found: 9689.286680 tokens (raw: **********, decimals: 6)
2025-06-22 15:40:08,387 - pumpportal_trader - INFO - [SCAN] POST-SELL TOKEN BALANCE CHECK: 9689.286680 tokens remaining
2025-06-22 15:40:08,387 - pumpportal_trader - ERROR - [ERROR] CRITICAL: SELL FAILED - No tokens sold! Balance: 9689.286680
2025-06-22 15:40:08,391 - pumpportal_trader - ERROR - [ERROR] This indicates the transaction failed or was fake!
2025-06-22 15:40:08,394 - utils - ERROR - sell_error: Failed PumpPortal sell command for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump [Context: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump]
2025-06-22 15:40:09,708 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:40:09,708 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:40:09,730 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:40:09,730 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:40:09,730 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00000945, Liquidity: $0
2025-06-22 15:40:09,731 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:40:09,731 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:40:09,755 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:40:09,755 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:40:09,756 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00000945, Liquidity: $0
2025-06-22 15:40:09,818 - pumpportal_trader - INFO - [MONEY] SELL FEES: Gas: 0.0001 SOL, Handling: 1.0%, Platform: 1.0%
2025-06-22 15:40:09,818 - pumpportal_trader - DEBUG - [SCAN] Checking token balance for 9VmPUjL5... in wallet 75eUxZsu...
2025-06-22 15:40:09,819 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:40:09,976 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:40:09,977 - pumpportal_trader - DEBUG - [SCAN] Found 1 token accounts for 9VmPUjL5...
2025-06-22 15:40:09,977 - pumpportal_trader - INFO - [SUCCESS] Token balance found: 9689.286680 tokens (raw: **********, decimals: 6)
2025-06-22 15:40:09,978 - pumpportal_trader - INFO - [SCAN] PRE-SELL TOKEN BALANCE: 9689.286680 tokens for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:40:09,978 - pumpportal_trader - INFO - [SCAN] Requested sell amount: 67.476383 tokens
2025-06-22 15:40:09,979 - pumpportal_trader - INFO - [RED] REAL SELL: 0.696% (67.4764/9689.2867 tokens) of 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:40:09,979 - pumpportal_trader - DEBUG - Priority fee: 0.******** SOL (100000 lamports)
2025-06-22 15:40:09,979 - pumpportal_trader - INFO - [FIX] CRITICAL FIX: Keeping percentage as string: '0.696%' (PumpPortal requires string format)
2025-06-22 15:40:09,979 - pumpportal_trader - INFO - [SCAN] DETAILED PumpPortal payload:
2025-06-22 15:40:09,980 - pumpportal_trader - INFO -    action: sell
2025-06-22 15:40:09,980 - pumpportal_trader - INFO -    mint: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:40:09,980 - pumpportal_trader - INFO -    amount: 0.696% (original: 0.696%)
2025-06-22 15:40:09,981 - pumpportal_trader - INFO -    denominatedInSol: false
2025-06-22 15:40:09,981 - pumpportal_trader - INFO -    slippage: 29
2025-06-22 15:40:09,982 - pumpportal_trader - INFO -    priorityFee: 0.0001 SOL
2025-06-22 15:40:09,982 - pumpportal_trader - INFO -    publicKey: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-22 15:40:09,982 - pumpportal_trader - INFO - [NETWORK] Using PumpPortal API: https://pumpportal.fun/api/trade-local (Network: MAINNET)
2025-06-22 15:40:09,984 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): pumpportal.fun:443
2025-06-22 15:40:10,748 - urllib3.connectionpool - DEBUG - https://pumpportal.fun:443 "POST /api/trade-local HTTP/1.1" 200 696
2025-06-22 15:40:10,748 - pumpportal_trader - INFO - [SIGNAL] PumpPortal API Response: 200
2025-06-22 15:40:10,751 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:40:10,966 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:40:10,967 - pumpportal_trader - INFO - Transaction sent successfully on attempt 1: 46VFFqcToGWpFr5kUfsgYd3Ze2gH67b1BBtxdxQnBkZA4ZU2MMQWjq7S465gkiH3AJp2BJNfyxLqF1hxTijuDWiH
2025-06-22 15:40:10,968 - pumpportal_trader - INFO - [SUCCESS] Transaction sent successfully: 46VFFqcToGWpFr5kUfsgYd3Ze2gH67b1BBtxdxQnBkZA4ZU2MMQWjq7S465gkiH3AJp2BJNfyxLqF1hxTijuDWiH
2025-06-22 15:40:10,968 - pumpportal_trader - INFO - [SCAN] CRITICAL FIX: Waiting for blockchain verification before confirming success...
2025-06-22 15:40:10,970 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:40:11,520 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:40:13,525 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:40:13,792 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:40:13,792 - pumpportal_trader - INFO - [SUCCESS] Transaction VERIFIED successful: 46VFFqcToGWpFr5kUfsgYd3Ze2gH67b1BBtxdxQnBkZA4ZU2MMQWjq7S465gkiH3AJp2BJNfyxLqF1hxTijuDWiH
2025-06-22 15:40:13,793 - pumpportal_trader - INFO - [SUCCESS] VERIFIED: Transaction confirmed on blockchain: 46VFFqcToGWpFr5kUfsgYd3Ze2gH67b1BBtxdxQnBkZA4ZU2MMQWjq7S465gkiH3AJp2BJNfyxLqF1hxTijuDWiH
2025-06-22 15:40:13,793 - pumpportal_trader - INFO - [SUCCESS] Sell successful: 46VFFqcToGWpFr5kUfsgYd3Ze2gH67b1BBtxdxQnBkZA4ZU2MMQWjq7S465gkiH3AJp2BJNfyxLqF1hxTijuDWiH (Gas cost: 0.0001 SOL)
2025-06-22 15:40:13,794 - pumpportal_trader - INFO - [SCAN] CRITICAL DEBUG: Transaction verified on blockchain: True
2025-06-22 15:40:13,794 - pumpportal_trader - INFO - 🔗 Solscan link: https://solscan.io/tx/46VFFqcToGWpFr5kUfsgYd3Ze2gH67b1BBtxdxQnBkZA4ZU2MMQWjq7S465gkiH3AJp2BJNfyxLqF1hxTijuDWiH
2025-06-22 15:40:13,795 - pumpportal_trader - DEBUG - [SCAN] Checking token balance for 9VmPUjL5... in wallet 75eUxZsu...
2025-06-22 15:40:13,797 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:40:14,037 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:40:14,038 - pumpportal_trader - DEBUG - [SCAN] Found 1 token accounts for 9VmPUjL5...
2025-06-22 15:40:14,038 - pumpportal_trader - INFO - [SUCCESS] Token balance found: 9689.286680 tokens (raw: **********, decimals: 6)
2025-06-22 15:40:14,039 - pumpportal_trader - INFO - [SCAN] POST-SELL TOKEN BALANCE CHECK: 9689.286680 tokens remaining
2025-06-22 15:40:14,039 - pumpportal_trader - ERROR - [ERROR] CRITICAL: SELL FAILED - No tokens sold! Balance: 9689.286680
2025-06-22 15:40:14,041 - pumpportal_trader - ERROR - [ERROR] This indicates the transaction failed or was fake!
2025-06-22 15:40:14,043 - utils - ERROR - sell_error: Failed PumpPortal sell command for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump [Context: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump]
2025-06-22 15:40:15,370 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:40:15,370 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:40:15,395 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:40:15,396 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:40:15,396 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00000945, Liquidity: $0
2025-06-22 15:40:15,398 - main - DEBUG - POSITION MONITORING: Getting price update for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:40:15,399 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:40:15,422 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump HTTP/1.1" 200 None
2025-06-22 15:40:15,422 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:40:15,423 - main - DEBUG - POSITION MONITORING: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump - Price: $0.00000945, Liquidity: $0
2025-06-22 15:40:15,425 - pumpportal_trader - INFO - [MONEY] SELL FEES: Gas: 0.0001 SOL, Handling: 1.0%, Platform: 1.0%
2025-06-22 15:40:15,426 - pumpportal_trader - DEBUG - [SCAN] Checking token balance for 9VmPUjL5... in wallet 75eUxZsu...
2025-06-22 15:40:15,427 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:40:15,603 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:40:15,604 - pumpportal_trader - DEBUG - [SCAN] Found 1 token accounts for 9VmPUjL5...
2025-06-22 15:40:15,604 - pumpportal_trader - INFO - [SUCCESS] Token balance found: 9689.286680 tokens (raw: **********, decimals: 6)
2025-06-22 15:40:15,605 - pumpportal_trader - INFO - [SCAN] PRE-SELL TOKEN BALANCE: 9689.286680 tokens for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:40:15,605 - pumpportal_trader - INFO - [SCAN] Requested sell amount: 67.476383 tokens
2025-06-22 15:40:15,606 - pumpportal_trader - INFO - [RED] REAL SELL: 0.696% (67.4764/9689.2867 tokens) of 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:40:15,606 - pumpportal_trader - DEBUG - Priority fee: 0.******** SOL (100000 lamports)
2025-06-22 15:40:15,606 - pumpportal_trader - INFO - [FIX] CRITICAL FIX: Keeping percentage as string: '0.696%' (PumpPortal requires string format)
2025-06-22 15:40:15,606 - pumpportal_trader - INFO - [SCAN] DETAILED PumpPortal payload:
2025-06-22 15:40:15,606 - pumpportal_trader - INFO -    action: sell
2025-06-22 15:40:15,607 - pumpportal_trader - INFO -    mint: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump
2025-06-22 15:40:15,607 - pumpportal_trader - INFO -    amount: 0.696% (original: 0.696%)
2025-06-22 15:40:15,607 - pumpportal_trader - INFO -    denominatedInSol: false
2025-06-22 15:40:15,607 - pumpportal_trader - INFO -    slippage: 29
2025-06-22 15:40:15,607 - pumpportal_trader - INFO -    priorityFee: 0.0001 SOL
2025-06-22 15:40:15,607 - pumpportal_trader - INFO -    publicKey: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-22 15:40:15,608 - pumpportal_trader - INFO - [NETWORK] Using PumpPortal API: https://pumpportal.fun/api/trade-local (Network: MAINNET)
2025-06-22 15:40:15,608 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): pumpportal.fun:443
2025-06-22 15:40:16,414 - urllib3.connectionpool - DEBUG - https://pumpportal.fun:443 "POST /api/trade-local HTTP/1.1" 200 696
2025-06-22 15:40:16,414 - pumpportal_trader - INFO - [SIGNAL] PumpPortal API Response: 200
2025-06-22 15:40:16,416 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:40:16,578 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:40:16,579 - pumpportal_trader - INFO - Transaction sent successfully on attempt 1: 5UoXjvobdJABTJjW45Zhun2WhDXsGGKp6CvYDdUPkRBfMfAWK6WddhgivFhfDsEYZywgRHsA1WqCkxg633S1nTYk
2025-06-22 15:40:16,579 - pumpportal_trader - INFO - [SUCCESS] Transaction sent successfully: 5UoXjvobdJABTJjW45Zhun2WhDXsGGKp6CvYDdUPkRBfMfAWK6WddhgivFhfDsEYZywgRHsA1WqCkxg633S1nTYk
2025-06-22 15:40:16,580 - pumpportal_trader - INFO - [SCAN] CRITICAL FIX: Waiting for blockchain verification before confirming success...
2025-06-22 15:40:16,582 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:40:17,002 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:40:19,018 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:40:19,278 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:40:19,279 - pumpportal_trader - INFO - [SUCCESS] Transaction VERIFIED successful: 5UoXjvobdJABTJjW45Zhun2WhDXsGGKp6CvYDdUPkRBfMfAWK6WddhgivFhfDsEYZywgRHsA1WqCkxg633S1nTYk
2025-06-22 15:40:19,279 - pumpportal_trader - INFO - [SUCCESS] VERIFIED: Transaction confirmed on blockchain: 5UoXjvobdJABTJjW45Zhun2WhDXsGGKp6CvYDdUPkRBfMfAWK6WddhgivFhfDsEYZywgRHsA1WqCkxg633S1nTYk
2025-06-22 15:40:19,280 - pumpportal_trader - INFO - [SUCCESS] Sell successful: 5UoXjvobdJABTJjW45Zhun2WhDXsGGKp6CvYDdUPkRBfMfAWK6WddhgivFhfDsEYZywgRHsA1WqCkxg633S1nTYk (Gas cost: 0.0001 SOL)
2025-06-22 15:40:19,280 - pumpportal_trader - INFO - [SCAN] CRITICAL DEBUG: Transaction verified on blockchain: True
2025-06-22 15:40:19,281 - pumpportal_trader - INFO - 🔗 Solscan link: https://solscan.io/tx/5UoXjvobdJABTJjW45Zhun2WhDXsGGKp6CvYDdUPkRBfMfAWK6WddhgivFhfDsEYZywgRHsA1WqCkxg633S1nTYk
2025-06-22 15:40:19,282 - pumpportal_trader - DEBUG - [SCAN] Checking token balance for 9VmPUjL5... in wallet 75eUxZsu...
2025-06-22 15:40:19,283 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:40:19,448 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:40:19,449 - pumpportal_trader - DEBUG - [SCAN] Found 1 token accounts for 9VmPUjL5...
2025-06-22 15:40:19,449 - pumpportal_trader - INFO - [SUCCESS] Token balance found: 9621.849245 tokens (raw: **********, decimals: 6)
2025-06-22 15:40:19,450 - pumpportal_trader - INFO - [SCAN] POST-SELL TOKEN BALANCE CHECK: 9621.849245 tokens remaining
2025-06-22 15:40:19,450 - pumpportal_trader - ERROR - [ERROR] CRITICAL: SELL FAILED - No tokens sold! Balance: 9621.849245
2025-06-22 15:40:19,451 - error_tracker - CRITICAL - [ALERT] ERROR PATTERN ALERT: pumpportal_trader occurred 5 times in 5 minutes
2025-06-22 15:40:19,451 - pumpportal_trader - ERROR - [ERROR] This indicates the transaction failed or was fake!
2025-06-22 15:40:19,452 - error_tracker - CRITICAL - [ALERT] ERROR PATTERN ALERT: pumpportal_trader occurred 6 times in 5 minutes
2025-06-22 15:40:19,454 - utils - ERROR - sell_error: Failed PumpPortal sell command for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump [Context: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump]
2025-06-22 15:40:20,568 - production_monitor - DEBUG - System healthy - CPU: 3.7%, Memory: 78.0%, Positions: 0
2025-06-22 15:40:56,409 - main - INFO - [FAST] Analyzing token: J9THMsYd9yxTZRnMsubwvVnbtD5oSGs6bUL1XT6apump
2025-06-22 15:40:56,413 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:40:56,648 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:40:56,649 - main - DEBUG - Token supply success: 1,000,000,000
2025-06-22 15:40:56,715 - urllib3.connectionpool - DEBUG - https://api.coingecko.com:443 "GET /api/v3/simple/price?ids=solana&vs_currencies=usd HTTP/1.1" 200 None
2025-06-22 15:40:56,715 - main - INFO - Fetched fresh SOL price: $132.77 (attempt 1)
2025-06-22 15:40:56,864 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/J9THMsYd9yxTZRnMsubwvVnbtD5oSGs6bUL1XT6apump HTTP/1.1" 200 None
2025-06-22 15:40:56,864 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-22 15:40:56,865 - main - INFO - Fetched data - SOL price: $132.77, Supply: 1,000,000,000
2025-06-22 15:40:56,865 - main - INFO - DexScreener data available - Price: $1.234e-05, Volume 24h: $5,049
2025-06-22 15:40:56,865 - main - INFO - [SCAN] Looking for pump.fun pool for J9THMsYd9yxTZRnMsubwvVnbtD5oSGs6bUL1XT6apump
2025-06-22 15:40:56,866 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:40:57,116 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:40:57,117 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:40:57,483 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:40:57,484 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:40:57,641 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:40:57,642 - main - DEBUG - Pool balance success: 24.727607 SOL
2025-06-22 15:40:57,642 - main - INFO - [SUCCESS] Found pool address: HkkD9duqb95AsjhPjP33oEXH39Dugxy5f2tFTdz62gJc
2025-06-22 15:40:57,643 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:40:57,869 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:40:57,869 - main - DEBUG - Pool balance success: 24.727607 SOL
2025-06-22 15:40:57,869 - main - INFO - [MONEY] Pool SOL balance: 24.727606927
2025-06-22 15:40:57,870 - main - INFO - [CHART] Calculated liquidity: $6,402 (SOL: 24.727606927 * $132.77 * 1.95)
2025-06-22 15:40:57,870 - main - INFO - Found pool HkkD9duq... with 24.727607 SOL
2025-06-22 15:40:57,870 - main - INFO - Using calculated liquidity: $6,402 (x1.95)
2025-06-22 15:40:57,871 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:40:58,247 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:40:58,248 - main - DEBUG - Holders success: 20 holders
2025-06-22 15:40:58,248 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-22 15:40:58,416 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-22 15:40:58,416 - main - INFO - [FAST] Analysis completed in 2.01s
2025-06-22 15:40:58,417 - main - INFO - [SCAN] MULTIPLIER CHECK ENABLED: Checking J9THMsYd9yxTZRnMsubwvVnbtD5oSGs6bUL1XT6apump for high multipliers (3x+)
2025-06-22 15:40:58,417 - main - INFO - [AIM] GMGN SIGNAL DETECTED: Allowing high multipliers for J9THMsYd9yxTZRnMsubwvVnbtD5oSGs6bUL1XT6apump
2025-06-22 15:40:58,418 - main - INFO - [AIM] GMGN signals are exempt from multiplier restrictions
2025-06-22 15:40:58,419 - main - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for J9THMsYd9yxTZRnMsubwvVnbtD5oSGs6bUL1XT6apump
2025-06-22 15:40:58,420 - main - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $6,402 < $7,000
2025-06-23 13:31:15,747 - unicode_test - INFO - [SUCCESS] Unicode-safe logging configured
2025-06-23 13:31:17,279 - unicode_test - INFO - [SUCCESS] Unicode-safe logging configured
2025-06-23 13:31:17,279 - main - INFO - Successfully configured Unicode-safe logging
2025-06-23 13:31:17,280 - main - INFO - Starting bot...
2025-06-23 13:31:17,281 - helius_rate_limiter - INFO - Helius Rate Limiter initialized: 50.0 RPS
2025-06-23 13:31:17,281 - main - INFO - [SUCCESS] BULLETPROOF SEPARATION: Analysis and Position Monitoring functions initialized
2025-06-23 13:31:17,281 - main - INFO - [SUCCESS] PERMANENT TRACKING: 0 analyzed, 0 skipped
2025-06-23 13:31:17,286 - pumpportal_trader - DEBUG - Using MAINNET RPC: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-23 13:31:17,286 - pumpportal_trader - INFO - Loaded transaction settings: {}
2025-06-23 13:31:17,287 - pumpportal_trader - INFO - Loaded configured default wallet: TEST1 with public key: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-23 13:31:17,287 - pumpportal_trader - INFO - PumpPortal Trader initialized for mainnet pump.fun trading
2025-06-23 13:31:17,287 - pumpportal_trader - INFO - Mainnet API URL: https://pumpportal.fun/api/trade-local
2025-06-23 13:31:17,287 - pumpportal_trader - INFO - [WARNING] Note: PumpPortal only supports mainnet
2025-06-23 13:31:17,287 - pumpportal_trader - INFO - RPC URL: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-23 13:31:17,287 - pumpportal_trader - INFO - Helius rate limiter: 50.0 RPS
2025-06-23 13:31:17,292 - unified_trader - INFO - Unified Trader initialized for mainnet real trading with adaptive slippage support
2025-06-23 13:31:17,292 - main - INFO - CRITICAL DEBUG: SIMPLE_PUMP_AVAILABLE = True
2025-06-23 13:31:17,293 - main - INFO - CRITICAL DEBUG: _fast_analysis_function = <function init_simple_pump_analyzer.<locals>.analyze_new_signal_only at 0x0000026E272EFEC0>
2025-06-23 13:31:17,293 - main - INFO - CRITICAL DEBUG: _position_monitoring_function = <function init_simple_pump_analyzer.<locals>.monitor_position_only at 0x0000026E272EFF60>
2025-06-23 13:31:17,293 - main - INFO - [SUCCESS] Fast analysis function connected to bot controller (NEW SIGNALS ONLY)
2025-06-23 13:31:17,293 - main - INFO - [SUCCESS] Position monitoring function connected to bot controller (BOUGHT POSITIONS ONLY)
2025-06-23 13:31:17,293 - main - INFO - [AIM] MULTIPLIER FILTERING: ENABLED - Will reject tokens with 5x+ gains (allowing up to 5x multiplier)
2025-06-23 13:31:17,294 - main - INFO - Initializing DEX adapters and sessions
2025-06-23 13:31:17,294 - main - INFO - DEX sessions initialized successfully
2025-06-23 13:31:17,294 - main - INFO - LLM integration is not available in this configuration
2025-06-23 13:31:17,294 - main - INFO - Registered direct signal callback for faster signal proyescessing
2025-06-23 13:31:17,294 - main - INFO - LLM integration is not available in this configuration
2025-06-23 13:31:17,295 - main - INFO - Initializing bot controller WITHOUT signal processing...
2025-06-23 13:31:18,443 - main - INFO - [SUCCESS] Bot controller initialized successfully WITHOUT signal processing
2025-06-23 13:31:18,443 - main - INFO - Started cache cleanup task
2025-06-23 13:31:18,456 - production_monitor - INFO - Production monitoring started
2025-06-23 13:31:18,456 - main - INFO - [SUCCESS] Production monitoring started
2025-06-23 13:31:18,457 - error_tracker - INFO - [SUCCESS] Error tracking enabled
2025-06-23 13:31:18,457 - main - INFO - [SUCCESS] Error tracking enabled
2025-06-23 13:31:18,459 - main - INFO - [SUCCESS] Trading metrics collector enabled
2025-06-23 13:31:18,459 - main - INFO - Starting CLI interface...
2025-06-23 13:31:18,463 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:31:18,670 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:31:18,675 - production_monitor - INFO - Production monitor started
2025-06-23 13:31:18,781 - production_monitor - DEBUG - System healthy - CPU: 7.1%, Memory: 76.2%, Positions: 0
2025-06-23 13:31:21,520 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:31:21,745 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:31:25,978 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:31:26,254 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:31:32,638 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:31:32,891 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:31:39,986 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:31:40,157 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:31:41,826 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:31:42,072 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:31:42,212 - pumpportal_trader - DEBUG - Using MAINNET RPC: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-23 13:31:42,212 - pumpportal_trader - INFO - Loaded transaction settings: {}
2025-06-23 13:31:42,214 - pumpportal_trader - INFO - Loaded configured default wallet: TEST1 with public key: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-23 13:31:42,214 - pumpportal_trader - INFO - PumpPortal Trader initialized for mainnet pump.fun trading
2025-06-23 13:31:42,214 - pumpportal_trader - INFO - Mainnet API URL: https://pumpportal.fun/api/trade-local
2025-06-23 13:31:42,214 - pumpportal_trader - INFO - [WARNING] Note: PumpPortal only supports mainnet
2025-06-23 13:31:42,214 - pumpportal_trader - INFO - RPC URL: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-23 13:31:42,214 - pumpportal_trader - INFO - Helius rate limiter: 50.0 RPS
2025-06-23 13:31:42,215 - unified_trader - INFO - [SUCCESS] PumpPortal trader initialized for mainnet
2025-06-23 13:31:42,215 - unified_trader - INFO - ℹ️ Helius trader disabled - devnet support removed
2025-06-23 13:31:42,215 - unified_trader - INFO - [SUCCESS] PumpPortal trader initialized successfully (mainnet only)
2025-06-23 13:31:42,216 - pumpportal_trader - DEBUG - Using MAINNET RPC: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-23 13:31:42,216 - pumpportal_trader - INFO - [REFRESH] PumpPortal trader set to MAINNET mode
2025-06-23 13:31:42,216 - pumpportal_trader - INFO - [NETWORK] Updated RPC URL: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-23 13:31:42,216 - pumpportal_trader - INFO - 🔗 API URL: https://pumpportal.fun/api/trade-local
2025-06-23 13:31:42,216 - unified_trader - INFO - [LAUNCH] Confirmed MAINNET mode - using PumpPortal for pump.fun
2025-06-23 13:31:44,729 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:31:45,214 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:08,292 - main - INFO - [FAST] Analyzing token: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:08,293 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.dexscreener.com:443
2025-06-23 13:32:08,294 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:08,294 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.coingecko.com:443
2025-06-23 13:32:08,538 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:08,538 - main - DEBUG - Token supply success: 1,000,000,000
2025-06-23 13:32:08,618 - urllib3.connectionpool - DEBUG - https://api.coingecko.com:443 "GET /api/v3/simple/price?ids=solana&vs_currencies=usd HTTP/1.1" 200 None
2025-06-23 13:32:08,618 - main - INFO - Fetched fresh SOL price: $134.4 (attempt 1)
2025-06-23 13:32:08,659 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS HTTP/1.1" 200 None
2025-06-23 13:32:08,660 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 13:32:08,660 - main - INFO - Fetched data - SOL price: $134.4, Supply: 1,000,000,000
2025-06-23 13:32:08,660 - main - INFO - DexScreener data available - Price: $3.31e-05, Volume 24h: $79,955
2025-06-23 13:32:08,660 - main - INFO - [SCAN] Looking for pump.fun pool for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:08,661 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:09,133 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:09,134 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:09,317 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:09,318 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:09,760 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:09,760 - main - DEBUG - Pool balance success: 56.879125 SOL
2025-06-23 13:32:09,760 - main - INFO - [SUCCESS] Found pool address: DKdJVLR2TChJA5CWhWjEkpyHAF5ck8YAqtFYFMy9kKrr
2025-06-23 13:32:09,761 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:10,161 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:10,161 - main - DEBUG - Pool balance success: 56.879125 SOL
2025-06-23 13:32:10,161 - main - INFO - [MONEY] Pool SOL balance: 56.879124827
2025-06-23 13:32:10,162 - main - INFO - [CHART] Calculated liquidity: $14,907 (SOL: 56.879124827 * $134.4 * 1.95)
2025-06-23 13:32:10,162 - main - INFO - Found pool DKdJVLR2... with 56.879125 SOL
2025-06-23 13:32:10,162 - main - INFO - Using calculated liquidity: $14,907 (x1.95)
2025-06-23 13:32:10,163 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:10,642 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:10,643 - main - DEBUG - Holders success: 20 holders
2025-06-23 13:32:10,644 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:10,927 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:10,928 - main - INFO - [FAST] Analysis completed in 2.64s
2025-06-23 13:32:10,928 - main - INFO - [SCAN] MULTIPLIER CHECK ENABLED: Checking 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS for high multipliers (3x+)
2025-06-23 13:32:10,928 - main - INFO - [AIM] GMGN SIGNAL DETECTED: Allowing high multipliers for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:10,928 - main - INFO - [AIM] GMGN signals are exempt from multiplier restrictions
2025-06-23 13:32:10,930 - main - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:10,930 - main - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $14,907, MC: $33,102, Whale: 18.6%
2025-06-23 13:32:10,930 - main - INFO - [SUCCESS] Fast analysis result: Price=$0.00003310, MC=$33,102, Liq=$14,907
2025-06-23 13:32:10,930 - main - DEBUG - PERMANENT STATE: 1 total, 0 skipped
2025-06-23 13:32:10,930 - main - DEBUG - Permanent decision recorded for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:10,935 - pumpportal_trader - INFO - [MONEY] EXTERNAL FEES: Buy tip: 0.0001 SOL, Gas: 0.0001 SOL, Handling: 0.0000 SOL, Platform: 0.0000 SOL
2025-06-23 13:32:10,935 - pumpportal_trader - INFO - [MONEY] TOTAL EXTERNAL FEES: 0.0002 SOL
2025-06-23 13:32:10,936 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:11,370 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:11,371 - pumpportal_trader - DEBUG - Wallet balance: 0.044942 SOL
2025-06-23 13:32:11,371 - pumpportal_trader - WARNING - [WARNING] Low wallet balance: 0.0449 SOL remaining
2025-06-23 13:32:11,371 - pumpportal_trader - INFO - [GREEN] REAL BUY: 0.0010 SOL of 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS (slippage: 21.0%) + 0.0002 SOL fees - Balance: 0.0449 SOL
2025-06-23 13:32:11,372 - pumpportal_trader - DEBUG - Priority fee: 0.******** SOL (100000 lamports)
2025-06-23 13:32:11,372 - pumpportal_trader - INFO - [SCAN] DETAILED PumpPortal payload:
2025-06-23 13:32:11,372 - pumpportal_trader - INFO -    action: buy
2025-06-23 13:32:11,373 - pumpportal_trader - INFO -    mint: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:11,373 - pumpportal_trader - INFO -    amount: 0.001 (original: 0.001)
2025-06-23 13:32:11,373 - pumpportal_trader - INFO -    denominatedInSol: true
2025-06-23 13:32:11,373 - pumpportal_trader - INFO -    slippage: 21
2025-06-23 13:32:11,373 - pumpportal_trader - INFO -    priorityFee: 0.0001 SOL
2025-06-23 13:32:11,373 - pumpportal_trader - INFO -    publicKey: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-23 13:32:11,374 - pumpportal_trader - INFO - [NETWORK] Using PumpPortal API: https://pumpportal.fun/api/trade-local (Network: MAINNET)
2025-06-23 13:32:11,374 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): pumpportal.fun:443
2025-06-23 13:32:12,226 - urllib3.connectionpool - DEBUG - https://pumpportal.fun:443 "POST /api/trade-local HTTP/1.1" 200 688
2025-06-23 13:32:12,226 - pumpportal_trader - INFO - [SIGNAL] PumpPortal API Response: 200
2025-06-23 13:32:12,228 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:12,436 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:12,437 - pumpportal_trader - INFO - Transaction sent successfully on attempt 1: 2aZRAbBiesnnoSmv8y49NipRAGNjNgi2Fs6kvrFyEeQKZ6rXRH6nGXh7xmPpEHtWXE3zyLBiGj8QpJADQ4MbMymm
2025-06-23 13:32:12,437 - pumpportal_trader - INFO - [SUCCESS] Transaction sent successfully: 2aZRAbBiesnnoSmv8y49NipRAGNjNgi2Fs6kvrFyEeQKZ6rXRH6nGXh7xmPpEHtWXE3zyLBiGj8QpJADQ4MbMymm
2025-06-23 13:32:12,438 - pumpportal_trader - INFO - [SCAN] CRITICAL FIX: Waiting for blockchain verification before confirming success...
2025-06-23 13:32:12,439 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:13,020 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:15,035 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:15,270 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:15,270 - pumpportal_trader - INFO - [SUCCESS] Transaction VERIFIED successful: 2aZRAbBiesnnoSmv8y49NipRAGNjNgi2Fs6kvrFyEeQKZ6rXRH6nGXh7xmPpEHtWXE3zyLBiGj8QpJADQ4MbMymm
2025-06-23 13:32:15,271 - pumpportal_trader - INFO - [SUCCESS] VERIFIED: Transaction confirmed on blockchain: 2aZRAbBiesnnoSmv8y49NipRAGNjNgi2Fs6kvrFyEeQKZ6rXRH6nGXh7xmPpEHtWXE3zyLBiGj8QpJADQ4MbMymm
2025-06-23 13:32:15,271 - pumpportal_trader - INFO - [SUCCESS] Buy successful: 2aZRAbBiesnnoSmv8y49NipRAGNjNgi2Fs6kvrFyEeQKZ6rXRH6nGXh7xmPpEHtWXE3zyLBiGj8QpJADQ4MbMymm (Total cost: 0.0012 SOL)
2025-06-23 13:32:15,435 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:15,713 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:18,892 - production_monitor - DEBUG - System healthy - CPU: 2.0%, Memory: 75.9%, Positions: 0
2025-06-23 13:32:23,036 - main - DEBUG - POSITION MONITORING: Getting price update for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:23,036 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:23,082 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS HTTP/1.1" 200 None
2025-06-23 13:32:23,083 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 13:32:23,083 - main - DEBUG - POSITION MONITORING: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS - Price: $0.00003310, Liquidity: $0
2025-06-23 13:32:24,389 - main - DEBUG - POSITION MONITORING: Getting price update for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:24,390 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:24,412 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS HTTP/1.1" 200 None
2025-06-23 13:32:24,413 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 13:32:24,413 - main - DEBUG - POSITION MONITORING: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS - Price: $0.00003310, Liquidity: $0
2025-06-23 13:32:25,728 - main - DEBUG - POSITION MONITORING: Getting price update for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:25,728 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:25,760 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS HTTP/1.1" 200 None
2025-06-23 13:32:25,760 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 13:32:25,760 - main - DEBUG - POSITION MONITORING: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS - Price: $0.00003310, Liquidity: $0
2025-06-23 13:32:27,078 - main - DEBUG - POSITION MONITORING: Getting price update for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:27,078 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:27,103 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS HTTP/1.1" 200 None
2025-06-23 13:32:27,103 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 13:32:27,104 - main - DEBUG - POSITION MONITORING: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS - Price: $0.00003310, Liquidity: $0
2025-06-23 13:32:28,417 - main - DEBUG - POSITION MONITORING: Getting price update for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:28,417 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:28,434 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS HTTP/1.1" 200 None
2025-06-23 13:32:28,435 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 13:32:28,435 - main - DEBUG - POSITION MONITORING: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS - Price: $0.00003310, Liquidity: $0
2025-06-23 13:32:29,751 - main - DEBUG - POSITION MONITORING: Getting price update for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:29,751 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:29,774 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS HTTP/1.1" 200 None
2025-06-23 13:32:29,775 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 13:32:29,775 - main - DEBUG - POSITION MONITORING: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS - Price: $0.00003310, Liquidity: $0
2025-06-23 13:32:31,086 - main - DEBUG - POSITION MONITORING: Getting price update for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:31,086 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:31,108 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS HTTP/1.1" 200 None
2025-06-23 13:32:31,108 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 13:32:31,108 - main - DEBUG - POSITION MONITORING: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS - Price: $0.00003310, Liquidity: $0
2025-06-23 13:32:32,420 - main - DEBUG - POSITION MONITORING: Getting price update for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:32,420 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:32,440 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS HTTP/1.1" 200 None
2025-06-23 13:32:32,440 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 13:32:32,440 - main - DEBUG - POSITION MONITORING: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS - Price: $0.00003310, Liquidity: $0
2025-06-23 13:32:33,751 - main - DEBUG - POSITION MONITORING: Getting price update for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:33,751 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:33,788 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS HTTP/1.1" 200 None
2025-06-23 13:32:33,788 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 13:32:33,788 - main - DEBUG - POSITION MONITORING: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS - Price: $0.00003310, Liquidity: $0
2025-06-23 13:32:35,103 - main - DEBUG - POSITION MONITORING: Getting price update for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:35,103 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:35,124 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS HTTP/1.1" 200 None
2025-06-23 13:32:35,125 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 13:32:35,125 - main - DEBUG - POSITION MONITORING: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS - Price: $0.00003310, Liquidity: $0
2025-06-23 13:32:36,432 - main - DEBUG - POSITION MONITORING: Getting price update for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:36,432 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:36,451 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS HTTP/1.1" 200 None
2025-06-23 13:32:36,452 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 13:32:36,452 - main - DEBUG - POSITION MONITORING: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS - Price: $0.00003310, Liquidity: $0
2025-06-23 13:32:37,767 - main - DEBUG - POSITION MONITORING: Getting price update for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:37,767 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:37,787 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS HTTP/1.1" 200 None
2025-06-23 13:32:37,787 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 13:32:37,787 - main - DEBUG - POSITION MONITORING: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS - Price: $0.00003310, Liquidity: $0
2025-06-23 13:32:39,093 - main - DEBUG - POSITION MONITORING: Getting price update for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:39,093 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:39,382 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS HTTP/1.1" 200 None
2025-06-23 13:32:39,382 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 13:32:39,382 - main - DEBUG - POSITION MONITORING: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS - Price: $0.00003729, Liquidity: $0
2025-06-23 13:32:39,385 - pumpportal_trader - INFO - [RED] BULLETPROOF SELL ATTEMPT 1/3: 30.*************** tokens of 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:39,386 - pumpportal_trader - INFO - [MONEY] BULLETPROOF FEES: Gas: 0.0001 SOL, Handling: 1.0%, Platform: 1.0%
2025-06-23 13:32:39,386 - pumpportal_trader - DEBUG - [SCAN] Checking token balance for 4ybHVUBC... (attempt 1/3)
2025-06-23 13:32:39,386 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:39,820 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:39,820 - pumpportal_trader - DEBUG - [SCAN] Found 1 token accounts for 4ybHVUBC...
2025-06-23 13:32:39,820 - pumpportal_trader - INFO - [SUCCESS] Token balance found: 3588.247741 tokens (raw: **********, decimals: 6)
2025-06-23 13:32:39,821 - pumpportal_trader - INFO - [SCAN] BULLETPROOF: Current token balance: 3588.247741 tokens
2025-06-23 13:32:39,821 - pumpportal_trader - INFO - [AIM] OVERRIDE: Ignoring token_amount 30.211480 - selling 100% of wallet
2025-06-23 13:32:39,821 - pumpportal_trader - INFO - [AIM] OVERRIDE: Main bot wanted to sell 30.211480 but wallet has 3588.247741
2025-06-23 13:32:39,821 - pumpportal_trader - INFO - [AIM] BULLETPROOF STRATEGY: Sell ALL tokens - 100% (3588.247741 tokens)
2025-06-23 13:32:39,821 - pumpportal_trader - INFO - [AIM] REASON: ALWAYS sell complete wallet balance regardless of input parameters
2025-06-23 13:32:39,822 - pumpportal_trader - INFO - [SCAN] PRE-SELL BALANCE STORED: 3588.247741 tokens
2025-06-23 13:32:39,822 - pumpportal_trader - DEBUG - Priority fee: 0.******** SOL (100000 lamports)
2025-06-23 13:32:39,822 - pumpportal_trader - INFO - [FIX] CRITICAL FIX: Keeping percentage as string: '100%' (PumpPortal requires string format)
2025-06-23 13:32:39,822 - pumpportal_trader - INFO - [SCAN] DETAILED PumpPortal payload:
2025-06-23 13:32:39,822 - pumpportal_trader - INFO -    action: sell
2025-06-23 13:32:39,823 - pumpportal_trader - INFO -    mint: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:39,823 - pumpportal_trader - INFO -    amount: 100% (original: 100%)
2025-06-23 13:32:39,823 - pumpportal_trader - INFO -    denominatedInSol: false
2025-06-23 13:32:39,823 - pumpportal_trader - INFO -    slippage: 29
2025-06-23 13:32:39,823 - pumpportal_trader - INFO -    priorityFee: 0.0001 SOL
2025-06-23 13:32:39,824 - pumpportal_trader - INFO -    publicKey: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-23 13:32:39,824 - pumpportal_trader - INFO - [NETWORK] Using PumpPortal API: https://pumpportal.fun/api/trade-local (Network: MAINNET)
2025-06-23 13:32:39,825 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): pumpportal.fun:443
2025-06-23 13:32:40,637 - urllib3.connectionpool - DEBUG - https://pumpportal.fun:443 "POST /api/trade-local HTTP/1.1" 200 696
2025-06-23 13:32:40,638 - pumpportal_trader - INFO - [SIGNAL] PumpPortal API Response: 200
2025-06-23 13:32:40,640 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:40,876 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:40,876 - pumpportal_trader - INFO - Transaction sent successfully on attempt 1: 4bqsxgZoYuGKi95XijNXMadu4CaiwXxHmvEmTnszhHwFSvc8PCrYnknSXkqFXei8Rq797W3qG3EGJDqfjozVEMFf
2025-06-23 13:32:40,876 - pumpportal_trader - INFO - [SUCCESS] Transaction sent successfully: 4bqsxgZoYuGKi95XijNXMadu4CaiwXxHmvEmTnszhHwFSvc8PCrYnknSXkqFXei8Rq797W3qG3EGJDqfjozVEMFf
2025-06-23 13:32:40,877 - pumpportal_trader - INFO - [SCAN] CRITICAL FIX: Waiting for blockchain verification before confirming success...
2025-06-23 13:32:40,877 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:41,446 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:43,459 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:43,758 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:43,759 - pumpportal_trader - INFO - [SUCCESS] Transaction VERIFIED successful: 4bqsxgZoYuGKi95XijNXMadu4CaiwXxHmvEmTnszhHwFSvc8PCrYnknSXkqFXei8Rq797W3qG3EGJDqfjozVEMFf
2025-06-23 13:32:43,759 - pumpportal_trader - INFO - [SUCCESS] VERIFIED: Transaction confirmed on blockchain: 4bqsxgZoYuGKi95XijNXMadu4CaiwXxHmvEmTnszhHwFSvc8PCrYnknSXkqFXei8Rq797W3qG3EGJDqfjozVEMFf
2025-06-23 13:32:43,760 - pumpportal_trader - INFO - [SUCCESS] BULLETPROOF SELL SUCCESS on attempt 1
2025-06-23 13:32:43,761 - pumpportal_trader - INFO - [SUCCESS] Sell successful: 4bqsxgZoYuGKi95XijNXMadu4CaiwXxHmvEmTnszhHwFSvc8PCrYnknSXkqFXei8Rq797W3qG3EGJDqfjozVEMFf (Gas cost: 0.0001 SOL)
2025-06-23 13:32:43,762 - pumpportal_trader - INFO - [SCAN] CRITICAL DEBUG: Transaction verified on blockchain: True
2025-06-23 13:32:43,762 - pumpportal_trader - INFO - 🔗 Solscan link: https://solscan.io/tx/4bqsxgZoYuGKi95XijNXMadu4CaiwXxHmvEmTnszhHwFSvc8PCrYnknSXkqFXei8Rq797W3qG3EGJDqfjozVEMFf
2025-06-23 13:32:43,763 - pumpportal_trader - DEBUG - [SCAN] Checking token balance for 4ybHVUBC... (attempt 1/3)
2025-06-23 13:32:43,764 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:43,977 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:43,977 - pumpportal_trader - DEBUG - [SCAN] Found 1 token accounts for 4ybHVUBC...
2025-06-23 13:32:43,977 - pumpportal_trader - INFO - [SUCCESS] Token balance found: 3588.247741 tokens (raw: **********, decimals: 6)
2025-06-23 13:32:43,978 - pumpportal_trader - INFO - [SCAN] POST-SELL TOKEN BALANCE CHECK: 3588.247741 tokens remaining
2025-06-23 13:32:43,978 - pumpportal_trader - WARNING - [SCAN] LARGE WALLET BALANCE: 3588.247741 tokens (suggests multiple buys)
2025-06-23 13:32:43,978 - pumpportal_trader - WARNING - [SCAN] Any reduction in balance will be considered a successful sell
2025-06-23 13:32:43,978 - pumpportal_trader - INFO - [SCAN] SELL VERIFICATION: Pre-sell: 3588.247741, Post-sell: 3588.247741, Sold: 0.000000
2025-06-23 13:32:43,979 - pumpportal_trader - ERROR - [ERROR] CRITICAL: SELL FAILED - No tokens sold! Balance: 3588.247741
2025-06-23 13:32:43,981 - pumpportal_trader - ERROR - [ERROR] This indicates the transaction failed or was fake!
2025-06-23 13:32:43,982 - utils - ERROR - sell_error: Failed PumpPortal sell command for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS [Context: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS]
2025-06-23 13:32:45,298 - main - DEBUG - POSITION MONITORING: Getting price update for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:45,298 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:45,320 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS HTTP/1.1" 200 None
2025-06-23 13:32:45,321 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 13:32:45,321 - main - DEBUG - POSITION MONITORING: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS - Price: $0.00003729, Liquidity: $0
2025-06-23 13:32:45,394 - pumpportal_trader - INFO - [RED] BULLETPROOF SELL ATTEMPT 1/3: 30.*************** tokens of 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:45,395 - pumpportal_trader - INFO - [MONEY] BULLETPROOF FEES: Gas: 0.0001 SOL, Handling: 1.0%, Platform: 1.0%
2025-06-23 13:32:45,395 - pumpportal_trader - DEBUG - [SCAN] Checking token balance for 4ybHVUBC... (attempt 1/3)
2025-06-23 13:32:45,396 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:45,648 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:45,648 - pumpportal_trader - DEBUG - [SCAN] Found 1 token accounts for 4ybHVUBC...
2025-06-23 13:32:45,648 - pumpportal_trader - INFO - [SUCCESS] Token balance found: 3588.247741 tokens (raw: **********, decimals: 6)
2025-06-23 13:32:45,648 - pumpportal_trader - INFO - [SCAN] BULLETPROOF: Current token balance: 3588.247741 tokens
2025-06-23 13:32:45,649 - pumpportal_trader - INFO - [AIM] OVERRIDE: Ignoring token_amount 30.211480 - selling 100% of wallet
2025-06-23 13:32:45,649 - pumpportal_trader - INFO - [AIM] OVERRIDE: Main bot wanted to sell 30.211480 but wallet has 3588.247741
2025-06-23 13:32:45,649 - pumpportal_trader - INFO - [AIM] BULLETPROOF STRATEGY: Sell ALL tokens - 100% (3588.247741 tokens)
2025-06-23 13:32:45,649 - pumpportal_trader - INFO - [AIM] REASON: ALWAYS sell complete wallet balance regardless of input parameters
2025-06-23 13:32:45,650 - pumpportal_trader - INFO - [SCAN] PRE-SELL BALANCE STORED: 3588.247741 tokens
2025-06-23 13:32:45,650 - pumpportal_trader - DEBUG - Priority fee: 0.******** SOL (100000 lamports)
2025-06-23 13:32:45,650 - pumpportal_trader - INFO - [FIX] CRITICAL FIX: Keeping percentage as string: '100%' (PumpPortal requires string format)
2025-06-23 13:32:45,650 - pumpportal_trader - INFO - [SCAN] DETAILED PumpPortal payload:
2025-06-23 13:32:45,650 - pumpportal_trader - INFO -    action: sell
2025-06-23 13:32:45,651 - pumpportal_trader - INFO -    mint: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:45,651 - pumpportal_trader - INFO -    amount: 100% (original: 100%)
2025-06-23 13:32:45,651 - pumpportal_trader - INFO -    denominatedInSol: false
2025-06-23 13:32:45,651 - pumpportal_trader - INFO -    slippage: 29
2025-06-23 13:32:45,651 - pumpportal_trader - INFO -    priorityFee: 0.0001 SOL
2025-06-23 13:32:45,651 - pumpportal_trader - INFO -    publicKey: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-23 13:32:45,651 - pumpportal_trader - INFO - [NETWORK] Using PumpPortal API: https://pumpportal.fun/api/trade-local (Network: MAINNET)
2025-06-23 13:32:45,652 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): pumpportal.fun:443
2025-06-23 13:32:46,444 - urllib3.connectionpool - DEBUG - https://pumpportal.fun:443 "POST /api/trade-local HTTP/1.1" 200 696
2025-06-23 13:32:46,445 - pumpportal_trader - INFO - [SIGNAL] PumpPortal API Response: 200
2025-06-23 13:32:46,446 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:46,683 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:46,684 - pumpportal_trader - INFO - Transaction sent successfully on attempt 1: 4bqsxgZoYuGKi95XijNXMadu4CaiwXxHmvEmTnszhHwFSvc8PCrYnknSXkqFXei8Rq797W3qG3EGJDqfjozVEMFf
2025-06-23 13:32:46,684 - pumpportal_trader - INFO - [SUCCESS] Transaction sent successfully: 4bqsxgZoYuGKi95XijNXMadu4CaiwXxHmvEmTnszhHwFSvc8PCrYnknSXkqFXei8Rq797W3qG3EGJDqfjozVEMFf
2025-06-23 13:32:46,684 - pumpportal_trader - INFO - [SCAN] CRITICAL FIX: Waiting for blockchain verification before confirming success...
2025-06-23 13:32:46,685 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:46,972 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:46,972 - pumpportal_trader - INFO - [SUCCESS] Transaction VERIFIED successful: 4bqsxgZoYuGKi95XijNXMadu4CaiwXxHmvEmTnszhHwFSvc8PCrYnknSXkqFXei8Rq797W3qG3EGJDqfjozVEMFf
2025-06-23 13:32:46,973 - pumpportal_trader - INFO - [SUCCESS] VERIFIED: Transaction confirmed on blockchain: 4bqsxgZoYuGKi95XijNXMadu4CaiwXxHmvEmTnszhHwFSvc8PCrYnknSXkqFXei8Rq797W3qG3EGJDqfjozVEMFf
2025-06-23 13:32:46,974 - pumpportal_trader - INFO - [SUCCESS] BULLETPROOF SELL SUCCESS on attempt 1
2025-06-23 13:32:46,974 - pumpportal_trader - INFO - [SUCCESS] Sell successful: 4bqsxgZoYuGKi95XijNXMadu4CaiwXxHmvEmTnszhHwFSvc8PCrYnknSXkqFXei8Rq797W3qG3EGJDqfjozVEMFf (Gas cost: 0.0001 SOL)
2025-06-23 13:32:46,974 - pumpportal_trader - INFO - [SCAN] CRITICAL DEBUG: Transaction verified on blockchain: True
2025-06-23 13:32:46,974 - pumpportal_trader - INFO - 🔗 Solscan link: https://solscan.io/tx/4bqsxgZoYuGKi95XijNXMadu4CaiwXxHmvEmTnszhHwFSvc8PCrYnknSXkqFXei8Rq797W3qG3EGJDqfjozVEMFf
2025-06-23 13:32:46,975 - pumpportal_trader - DEBUG - [SCAN] Checking token balance for 4ybHVUBC... (attempt 1/3)
2025-06-23 13:32:46,975 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:47,398 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:47,399 - pumpportal_trader - DEBUG - [SCAN] Found 1 token accounts for 4ybHVUBC...
2025-06-23 13:32:47,399 - pumpportal_trader - INFO - [SUCCESS] Token balance found: 3588.247741 tokens (raw: **********, decimals: 6)
2025-06-23 13:32:47,399 - pumpportal_trader - INFO - [SCAN] POST-SELL TOKEN BALANCE CHECK: 3588.247741 tokens remaining
2025-06-23 13:32:47,400 - pumpportal_trader - WARNING - [SCAN] LARGE WALLET BALANCE: 3588.247741 tokens (suggests multiple buys)
2025-06-23 13:32:47,400 - pumpportal_trader - WARNING - [SCAN] Any reduction in balance will be considered a successful sell
2025-06-23 13:32:47,400 - pumpportal_trader - INFO - [SCAN] SELL VERIFICATION: Pre-sell: 3588.247741, Post-sell: 3588.247741, Sold: 0.000000
2025-06-23 13:32:47,400 - pumpportal_trader - ERROR - [ERROR] CRITICAL: SELL FAILED - No tokens sold! Balance: 3588.247741
2025-06-23 13:32:47,401 - pumpportal_trader - ERROR - [ERROR] This indicates the transaction failed or was fake!
2025-06-23 13:32:47,402 - utils - ERROR - sell_error: Failed PumpPortal sell command for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS [Context: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS]
2025-06-23 13:32:48,719 - main - DEBUG - POSITION MONITORING: Getting price update for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:48,719 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:48,740 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS HTTP/1.1" 200 None
2025-06-23 13:32:48,740 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 13:32:48,741 - main - DEBUG - POSITION MONITORING: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS - Price: $0.00003729, Liquidity: $0
2025-06-23 13:32:48,746 - pumpportal_trader - INFO - [RED] BULLETPROOF SELL ATTEMPT 1/3: 30.*************** tokens of 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:48,746 - pumpportal_trader - INFO - [MONEY] BULLETPROOF FEES: Gas: 0.0001 SOL, Handling: 1.0%, Platform: 1.0%
2025-06-23 13:32:48,747 - pumpportal_trader - DEBUG - [SCAN] Checking token balance for 4ybHVUBC... (attempt 1/3)
2025-06-23 13:32:48,747 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:49,008 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:49,010 - pumpportal_trader - DEBUG - [SCAN] Found 1 token accounts for 4ybHVUBC...
2025-06-23 13:32:49,010 - pumpportal_trader - INFO - [SUCCESS] Token balance found: 3588.247741 tokens (raw: **********, decimals: 6)
2025-06-23 13:32:49,010 - pumpportal_trader - INFO - [SCAN] BULLETPROOF: Current token balance: 3588.247741 tokens
2025-06-23 13:32:49,011 - pumpportal_trader - INFO - [AIM] OVERRIDE: Ignoring token_amount 30.211480 - selling 100% of wallet
2025-06-23 13:32:49,012 - pumpportal_trader - INFO - [AIM] OVERRIDE: Main bot wanted to sell 30.211480 but wallet has 3588.247741
2025-06-23 13:32:49,012 - pumpportal_trader - INFO - [AIM] BULLETPROOF STRATEGY: Sell ALL tokens - 100% (3588.247741 tokens)
2025-06-23 13:32:49,013 - pumpportal_trader - INFO - [AIM] REASON: ALWAYS sell complete wallet balance regardless of input parameters
2025-06-23 13:32:49,013 - pumpportal_trader - INFO - [SCAN] PRE-SELL BALANCE STORED: 3588.247741 tokens
2025-06-23 13:32:49,014 - pumpportal_trader - DEBUG - Priority fee: 0.******** SOL (100000 lamports)
2025-06-23 13:32:49,014 - pumpportal_trader - INFO - [FIX] CRITICAL FIX: Keeping percentage as string: '100%' (PumpPortal requires string format)
2025-06-23 13:32:49,014 - pumpportal_trader - INFO - [SCAN] DETAILED PumpPortal payload:
2025-06-23 13:32:49,014 - pumpportal_trader - INFO -    action: sell
2025-06-23 13:32:49,015 - pumpportal_trader - INFO -    mint: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:49,015 - pumpportal_trader - INFO -    amount: 100% (original: 100%)
2025-06-23 13:32:49,015 - pumpportal_trader - INFO -    denominatedInSol: false
2025-06-23 13:32:49,015 - pumpportal_trader - INFO -    slippage: 29
2025-06-23 13:32:49,015 - pumpportal_trader - INFO -    priorityFee: 0.0001 SOL
2025-06-23 13:32:49,015 - pumpportal_trader - INFO -    publicKey: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-23 13:32:49,016 - pumpportal_trader - INFO - [NETWORK] Using PumpPortal API: https://pumpportal.fun/api/trade-local (Network: MAINNET)
2025-06-23 13:32:49,017 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): pumpportal.fun:443
2025-06-23 13:32:49,814 - urllib3.connectionpool - DEBUG - https://pumpportal.fun:443 "POST /api/trade-local HTTP/1.1" 200 696
2025-06-23 13:32:49,814 - pumpportal_trader - INFO - [SIGNAL] PumpPortal API Response: 200
2025-06-23 13:32:49,816 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:50,038 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:50,038 - pumpportal_trader - INFO - Transaction sent successfully on attempt 1: 5M6uhg2oEUcy6emRT67qh2LLVRCcWUMYDgBdZm51QRxgmTcMJ8zLLJ41yqmbtRrNon1Hjh1CukYxW66E31R42v6X
2025-06-23 13:32:50,038 - pumpportal_trader - INFO - [SUCCESS] Transaction sent successfully: 5M6uhg2oEUcy6emRT67qh2LLVRCcWUMYDgBdZm51QRxgmTcMJ8zLLJ41yqmbtRrNon1Hjh1CukYxW66E31R42v6X
2025-06-23 13:32:50,039 - pumpportal_trader - INFO - [SCAN] CRITICAL FIX: Waiting for blockchain verification before confirming success...
2025-06-23 13:32:50,039 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:50,460 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:52,476 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:52,641 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:52,642 - pumpportal_trader - ERROR - [ERROR] Transaction FAILED on blockchain: 5M6uhg2oEUcy6emRT67qh2LLVRCcWUMYDgBdZm51QRxgmTcMJ8zLLJ41yqmbtRrNon1Hjh1CukYxW66E31R42v6X - {'InstructionError': [3, {'Custom': 6022}]}
2025-06-23 13:32:52,643 - pumpportal_trader - ERROR - [ERROR] CRITICAL: Transaction FAILED verification on blockchain: 5M6uhg2oEUcy6emRT67qh2LLVRCcWUMYDgBdZm51QRxgmTcMJ8zLLJ41yqmbtRrNon1Hjh1CukYxW66E31R42v6X
2025-06-23 13:32:52,644 - pumpportal_trader - WARNING - [WARNING] SELL ATTEMPT 1 FAILED: Transaction failed blockchain verification
2025-06-23 13:32:52,644 - pumpportal_trader - INFO - [REFRESH] Will retry with higher slippage in 3 seconds...
2025-06-23 13:32:55,647 - pumpportal_trader - INFO - [RED] BULLETPROOF SELL ATTEMPT 2/3: 30.*************** tokens of 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:55,647 - pumpportal_trader - INFO - [REFRESH] RETRY 2: Increasing slippage to 34.0% for better success rate
2025-06-23 13:32:55,648 - pumpportal_trader - INFO - [MONEY] BULLETPROOF FEES: Gas: 0.0002 SOL, Handling: 1.0%, Platform: 1.0%
2025-06-23 13:32:55,648 - pumpportal_trader - DEBUG - [SCAN] Checking token balance for 4ybHVUBC... (attempt 1/3)
2025-06-23 13:32:55,650 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:56,107 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:56,108 - pumpportal_trader - DEBUG - [SCAN] Found 0 token accounts for 4ybHVUBC...
2025-06-23 13:32:56,108 - pumpportal_trader - DEBUG - [SCAN] No token accounts found for 4ybHVUBC... - wallet has 0 balance
2025-06-23 13:32:56,108 - pumpportal_trader - ERROR - [ERROR] BULLETPROOF: No token balance found for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS! Cannot sell tokens we don't have.
2025-06-23 13:32:56,109 - pumpportal_trader - INFO - [REFRESH] Will retry in 3 seconds...
2025-06-23 13:32:59,120 - pumpportal_trader - INFO - [RED] BULLETPROOF SELL ATTEMPT 3/3: 30.*************** tokens of 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:32:59,120 - pumpportal_trader - INFO - [REFRESH] RETRY 3: Increasing slippage to 39.0% for better success rate
2025-06-23 13:32:59,121 - pumpportal_trader - INFO - [MONEY] BULLETPROOF FEES: Gas: 0.0002 SOL, Handling: 1.0%, Platform: 1.0%
2025-06-23 13:32:59,121 - pumpportal_trader - DEBUG - [SCAN] Checking token balance for 4ybHVUBC... (attempt 1/3)
2025-06-23 13:32:59,122 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:32:59,302 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:32:59,303 - pumpportal_trader - DEBUG - [SCAN] Found 0 token accounts for 4ybHVUBC...
2025-06-23 13:32:59,303 - pumpportal_trader - DEBUG - [SCAN] No token accounts found for 4ybHVUBC... - wallet has 0 balance
2025-06-23 13:32:59,303 - pumpportal_trader - ERROR - [ERROR] BULLETPROOF: No token balance found for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS! Cannot sell tokens we don't have.
2025-06-23 13:32:59,305 - utils - ERROR - sell_error: Failed PumpPortal sell command for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS [Context: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS]
2025-06-23 13:47:40,867 - unicode_test - INFO - [SUCCESS] Unicode-safe logging configured
2025-06-23 13:47:46,036 - unicode_test - INFO - [SUCCESS] Unicode-safe logging configured
2025-06-23 13:47:46,036 - main - INFO - Successfully configured Unicode-safe logging
2025-06-23 13:47:46,037 - main - INFO - Starting bot...
2025-06-23 13:47:46,038 - helius_rate_limiter - INFO - Helius Rate Limiter initialized: 50.0 RPS
2025-06-23 13:47:46,038 - main - INFO - [SUCCESS] BULLETPROOF SEPARATION: Analysis and Position Monitoring functions initialized
2025-06-23 13:47:46,038 - main - INFO - [SUCCESS] PERMANENT TRACKING: 0 analyzed, 0 skipped
2025-06-23 13:47:46,043 - pumpportal_trader - DEBUG - Using MAINNET RPC: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-23 13:47:46,043 - pumpportal_trader - INFO - Loaded transaction settings: {}
2025-06-23 13:47:46,045 - pumpportal_trader - INFO - Loaded configured default wallet: TEST1 with public key: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-23 13:47:46,045 - pumpportal_trader - INFO - PumpPortal Trader initialized for mainnet pump.fun trading
2025-06-23 13:47:46,045 - pumpportal_trader - INFO - Mainnet API URL: https://pumpportal.fun/api/trade-local
2025-06-23 13:47:46,045 - pumpportal_trader - INFO - [WARNING] Note: PumpPortal only supports mainnet
2025-06-23 13:47:46,045 - pumpportal_trader - INFO - RPC URL: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-23 13:47:46,045 - pumpportal_trader - INFO - Helius rate limiter: 50.0 RPS
2025-06-23 13:47:46,046 - unified_trader - INFO - Unified Trader initialized for mainnet real trading with adaptive slippage support
2025-06-23 13:47:46,047 - main - INFO - CRITICAL DEBUG: SIMPLE_PUMP_AVAILABLE = True
2025-06-23 13:47:46,047 - main - INFO - CRITICAL DEBUG: _fast_analysis_function = <function init_simple_pump_analyzer.<locals>.analyze_new_signal_only at 0x000001528F7FFEC0>
2025-06-23 13:47:46,047 - main - INFO - CRITICAL DEBUG: _position_monitoring_function = <function init_simple_pump_analyzer.<locals>.monitor_position_only at 0x000001528F7FFF60>
2025-06-23 13:47:46,047 - main - INFO - [SUCCESS] Fast analysis function connected to bot controller (NEW SIGNALS ONLY)
2025-06-23 13:47:46,048 - main - INFO - [SUCCESS] Position monitoring function connected to bot controller (BOUGHT POSITIONS ONLY)
2025-06-23 13:47:46,048 - main - INFO - [AIM] MULTIPLIER FILTERING: ENABLED - Will reject tokens with 5x+ gains (allowing up to 5x multiplier)
2025-06-23 13:47:46,048 - main - INFO - Initializing DEX adapters and sessions
2025-06-23 13:47:46,048 - main - INFO - DEX sessions initialized successfully
2025-06-23 13:47:46,048 - main - INFO - LLM integration is not available in this configuration
2025-06-23 13:47:46,049 - main - INFO - Registered direct signal callback for faster signal proyescessing
2025-06-23 13:47:46,049 - main - INFO - LLM integration is not available in this configuration
2025-06-23 13:47:46,049 - main - INFO - Initializing bot controller WITHOUT signal processing...
2025-06-23 13:47:48,702 - main - INFO - [SUCCESS] Bot controller initialized successfully WITHOUT signal processing
2025-06-23 13:47:48,702 - main - INFO - Started cache cleanup task
2025-06-23 13:47:48,717 - production_monitor - INFO - Production monitoring started
2025-06-23 13:47:48,718 - main - INFO - [SUCCESS] Production monitoring started
2025-06-23 13:47:48,719 - error_tracker - INFO - [SUCCESS] Error tracking enabled
2025-06-23 13:47:48,719 - main - INFO - [SUCCESS] Error tracking enabled
2025-06-23 13:47:48,721 - main - INFO - [SUCCESS] Trading metrics collector enabled
2025-06-23 13:47:48,721 - main - INFO - Starting CLI interface...
2025-06-23 13:47:48,726 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:47:49,129 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:47:49,136 - production_monitor - INFO - Production monitor started
2025-06-23 13:47:49,242 - production_monitor - DEBUG - System healthy - CPU: 25.0%, Memory: 77.6%, Positions: 0
2025-06-23 13:48:03,477 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:48:03,735 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:48:10,020 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:48:10,202 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:48:12,201 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:48:12,638 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:48:13,327 - pumpportal_trader - DEBUG - Using MAINNET RPC: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-23 13:48:13,327 - pumpportal_trader - INFO - Loaded transaction settings: {}
2025-06-23 13:48:13,329 - pumpportal_trader - INFO - Loaded configured default wallet: TEST1 with public key: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-23 13:48:13,329 - pumpportal_trader - INFO - PumpPortal Trader initialized for mainnet pump.fun trading
2025-06-23 13:48:13,329 - pumpportal_trader - INFO - Mainnet API URL: https://pumpportal.fun/api/trade-local
2025-06-23 13:48:13,329 - pumpportal_trader - INFO - [WARNING] Note: PumpPortal only supports mainnet
2025-06-23 13:48:13,329 - pumpportal_trader - INFO - RPC URL: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-23 13:48:13,329 - pumpportal_trader - INFO - Helius rate limiter: 50.0 RPS
2025-06-23 13:48:13,329 - unified_trader - INFO - [SUCCESS] PumpPortal trader initialized for mainnet
2025-06-23 13:48:13,329 - unified_trader - INFO - ℹ️ Helius trader disabled - devnet support removed
2025-06-23 13:48:13,329 - unified_trader - INFO - [SUCCESS] PumpPortal trader initialized successfully (mainnet only)
2025-06-23 13:48:13,331 - pumpportal_trader - DEBUG - Using MAINNET RPC: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-23 13:48:13,331 - pumpportal_trader - INFO - [REFRESH] PumpPortal trader set to MAINNET mode
2025-06-23 13:48:13,331 - pumpportal_trader - INFO - [NETWORK] Updated RPC URL: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-23 13:48:13,331 - pumpportal_trader - INFO - 🔗 API URL: https://pumpportal.fun/api/trade-local
2025-06-23 13:48:13,331 - unified_trader - INFO - [LAUNCH] Confirmed MAINNET mode - using PumpPortal for pump.fun
2025-06-23 13:48:14,236 - main - DEBUG - POSITION MONITORING: Getting price update for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:48:14,236 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:48:14,238 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.dexscreener.com:443
2025-06-23 13:48:14,597 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS HTTP/1.1" 200 None
2025-06-23 13:48:14,598 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 13:48:14,598 - main - DEBUG - POSITION MONITORING: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS - Price: $0.00002979, Liquidity: $0
2025-06-23 13:48:14,602 - pumpportal_trader - INFO - [RED] BULLETPROOF SELL ATTEMPT 1/3: 30.*************** tokens of 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:48:14,602 - pumpportal_trader - INFO - [MONEY] BULLETPROOF FEES: Gas: 0.0001 SOL, Handling: 1.0%, Platform: 1.0%
2025-06-23 13:48:14,602 - pumpportal_trader - DEBUG - [SCAN] Checking token balance for 4ybHVUBC... (attempt 1/3)
2025-06-23 13:48:14,603 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:48:14,774 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:48:14,775 - pumpportal_trader - DEBUG - [SCAN] Found 0 token accounts for 4ybHVUBC...
2025-06-23 13:48:14,775 - pumpportal_trader - DEBUG - [SCAN] No token accounts found for 4ybHVUBC... - wallet has 0 balance
2025-06-23 13:48:14,775 - pumpportal_trader - ERROR - [ERROR] BULLETPROOF: No token balance found for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS! Cannot sell tokens we don't have.
2025-06-23 13:48:14,776 - pumpportal_trader - INFO - [REFRESH] Will retry in 3 seconds...
2025-06-23 13:48:15,862 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:48:16,076 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:48:17,790 - pumpportal_trader - INFO - [RED] BULLETPROOF SELL ATTEMPT 2/3: 30.*************** tokens of 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:48:17,791 - pumpportal_trader - INFO - [REFRESH] RETRY 2: Increasing slippage to 34.0% for better success rate
2025-06-23 13:48:17,791 - pumpportal_trader - INFO - [MONEY] BULLETPROOF FEES: Gas: 0.0002 SOL, Handling: 1.0%, Platform: 1.0%
2025-06-23 13:48:17,792 - pumpportal_trader - DEBUG - [SCAN] Checking token balance for 4ybHVUBC... (attempt 1/3)
2025-06-23 13:48:17,794 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:48:18,212 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:48:18,212 - pumpportal_trader - DEBUG - [SCAN] Found 0 token accounts for 4ybHVUBC...
2025-06-23 13:48:18,213 - pumpportal_trader - DEBUG - [SCAN] No token accounts found for 4ybHVUBC... - wallet has 0 balance
2025-06-23 13:48:18,213 - pumpportal_trader - ERROR - [ERROR] BULLETPROOF: No token balance found for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS! Cannot sell tokens we don't have.
2025-06-23 13:48:18,213 - pumpportal_trader - INFO - [REFRESH] Will retry in 3 seconds...
2025-06-23 13:48:21,221 - pumpportal_trader - INFO - [RED] BULLETPROOF SELL ATTEMPT 3/3: 30.*************** tokens of 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:48:21,222 - pumpportal_trader - INFO - [REFRESH] RETRY 3: Increasing slippage to 39.0% for better success rate
2025-06-23 13:48:21,222 - pumpportal_trader - INFO - [MONEY] BULLETPROOF FEES: Gas: 0.0002 SOL, Handling: 1.0%, Platform: 1.0%
2025-06-23 13:48:21,222 - pumpportal_trader - DEBUG - [SCAN] Checking token balance for 4ybHVUBC... (attempt 1/3)
2025-06-23 13:48:21,223 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:48:21,709 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:48:21,710 - pumpportal_trader - DEBUG - [SCAN] Found 0 token accounts for 4ybHVUBC...
2025-06-23 13:48:21,710 - pumpportal_trader - DEBUG - [SCAN] No token accounts found for 4ybHVUBC... - wallet has 0 balance
2025-06-23 13:48:21,710 - pumpportal_trader - ERROR - [ERROR] BULLETPROOF: No token balance found for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS! Cannot sell tokens we don't have.
2025-06-23 13:48:21,711 - utils - ERROR - sell_error: Failed PumpPortal sell command for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS [Context: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS]
2025-06-23 13:48:23,026 - main - DEBUG - POSITION MONITORING: Getting price update for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:48:23,026 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:48:23,048 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS HTTP/1.1" 200 None
2025-06-23 13:48:23,048 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 13:48:23,049 - main - DEBUG - POSITION MONITORING: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS - Price: $0.00002979, Liquidity: $0
2025-06-23 13:48:23,136 - pumpportal_trader - INFO - [RED] BULLETPROOF SELL ATTEMPT 1/3: 30.*************** tokens of 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:48:23,136 - pumpportal_trader - INFO - [MONEY] BULLETPROOF FEES: Gas: 0.0001 SOL, Handling: 1.0%, Platform: 1.0%
2025-06-23 13:48:23,136 - pumpportal_trader - DEBUG - [SCAN] Checking token balance for 4ybHVUBC... (attempt 1/3)
2025-06-23 13:48:23,137 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:48:23,564 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:48:23,565 - pumpportal_trader - DEBUG - [SCAN] Found 0 token accounts for 4ybHVUBC...
2025-06-23 13:48:23,565 - pumpportal_trader - DEBUG - [SCAN] No token accounts found for 4ybHVUBC... - wallet has 0 balance
2025-06-23 13:48:23,565 - pumpportal_trader - ERROR - [ERROR] BULLETPROOF: No token balance found for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS! Cannot sell tokens we don't have.
2025-06-23 13:48:23,565 - pumpportal_trader - INFO - [REFRESH] Will retry in 3 seconds...
2025-06-23 13:48:26,528 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:48:26,942 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:48:26,947 - pumpportal_trader - INFO - [RED] BULLETPROOF SELL ATTEMPT 2/3: 30.*************** tokens of 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:48:26,947 - pumpportal_trader - INFO - [REFRESH] RETRY 2: Increasing slippage to 34.0% for better success rate
2025-06-23 13:48:26,948 - pumpportal_trader - INFO - [MONEY] BULLETPROOF FEES: Gas: 0.0002 SOL, Handling: 1.0%, Platform: 1.0%
2025-06-23 13:48:26,948 - pumpportal_trader - DEBUG - [SCAN] Checking token balance for 4ybHVUBC... (attempt 1/3)
2025-06-23 13:48:26,949 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:48:27,225 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:48:27,226 - pumpportal_trader - DEBUG - [SCAN] Found 0 token accounts for 4ybHVUBC...
2025-06-23 13:48:27,226 - pumpportal_trader - DEBUG - [SCAN] No token accounts found for 4ybHVUBC... - wallet has 0 balance
2025-06-23 13:48:27,226 - pumpportal_trader - ERROR - [ERROR] BULLETPROOF: No token balance found for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS! Cannot sell tokens we don't have.
2025-06-23 13:48:27,227 - error_tracker - CRITICAL - [ALERT] ERROR PATTERN ALERT: pumpportal_trader occurred 5 times in 5 minutes
2025-06-23 13:48:27,227 - pumpportal_trader - INFO - [REFRESH] Will retry in 3 seconds...
2025-06-23 13:48:29,163 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:48:29,362 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:48:29,385 - main - INFO - [FAST] Analyzing token: yHxtomrUdwb5ia2Rb5KRnKm7j93sYh8iRYEdmmzpump
2025-06-23 13:48:29,387 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:48:29,387 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.coingecko.com:443
2025-06-23 13:48:29,686 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:48:29,686 - main - DEBUG - Token supply success: 999,745,235
2025-06-23 13:48:29,741 - urllib3.connectionpool - DEBUG - https://api.coingecko.com:443 "GET /api/v3/simple/price?ids=solana&vs_currencies=usd HTTP/1.1" 200 None
2025-06-23 13:48:29,742 - main - INFO - Fetched fresh SOL price: $134.64 (attempt 1)
2025-06-23 13:48:29,768 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/yHxtomrUdwb5ia2Rb5KRnKm7j93sYh8iRYEdmmzpump HTTP/1.1" 200 None
2025-06-23 13:48:29,769 - main - DEBUG - DexScreener success (attempt 1): 4 pairs found
2025-06-23 13:48:29,769 - main - INFO - Fetched data - SOL price: $134.64, Supply: 999,745,235
2025-06-23 13:48:29,769 - main - INFO - DexScreener data available - Price: $0.0004496, Volume 24h: $1,223,825
2025-06-23 13:48:29,769 - main - INFO - Using DexScreener liquidity: $72,708 (MC: $449,548)
2025-06-23 13:48:29,770 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:48:30,069 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:48:30,069 - main - DEBUG - Holders success: 20 holders
2025-06-23 13:48:30,070 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:48:30,501 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:48:30,502 - main - INFO - [FAST] Analysis completed in 1.12s
2025-06-23 13:48:30,504 - main - INFO - [SCAN] MULTIPLIER CHECK ENABLED: Checking yHxtomrUdwb5ia2Rb5KRnKm7j93sYh8iRYEdmmzpump for high multipliers (3x+)
2025-06-23 13:48:30,504 - main - INFO - [CHART] PRICE CHANGES: 5m: 86.8%, 1h: 164.0%, 6h: 411.0%, 24h: 199.0% | Max: 411.0%
2025-06-23 13:48:30,505 - main - WARNING - 🚫 REJECTING HIGH MULTIPLIER TOKEN: yHxtomrUdwb5ia2Rb5KRnKm7j93sYh8iRYEdmmzpump
2025-06-23 13:48:30,506 - main - WARNING -    Max price change: 411.0% (>= 400% = 5x)
2025-06-23 13:48:30,506 - main - WARNING -    REASON: Only buying coins with maximum 5x multiplier
2025-06-23 13:48:30,507 - main - WARNING -    NOTE: GMGN signals are exempt from this restriction
2025-06-23 13:48:30,515 - pumpportal_trader - INFO - [RED] BULLETPROOF SELL ATTEMPT 3/3: 30.*************** tokens of 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS
2025-06-23 13:48:30,515 - pumpportal_trader - INFO - [REFRESH] RETRY 3: Increasing slippage to 39.0% for better success rate
2025-06-23 13:48:30,515 - pumpportal_trader - INFO - [MONEY] BULLETPROOF FEES: Gas: 0.0002 SOL, Handling: 1.0%, Platform: 1.0%
2025-06-23 13:48:30,516 - pumpportal_trader - DEBUG - [SCAN] Checking token balance for 4ybHVUBC... (attempt 1/3)
2025-06-23 13:48:30,517 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:48:30,667 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:48:30,668 - pumpportal_trader - DEBUG - [SCAN] Found 0 token accounts for 4ybHVUBC...
2025-06-23 13:48:30,668 - pumpportal_trader - DEBUG - [SCAN] No token accounts found for 4ybHVUBC... - wallet has 0 balance
2025-06-23 13:48:30,668 - pumpportal_trader - ERROR - [ERROR] BULLETPROOF: No token balance found for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS! Cannot sell tokens we don't have.
2025-06-23 13:48:30,668 - error_tracker - CRITICAL - [ALERT] ERROR PATTERN ALERT: pumpportal_trader occurred 6 times in 5 minutes
2025-06-23 13:48:30,671 - utils - ERROR - sell_error: Failed PumpPortal sell command for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS [Context: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS]
2025-06-23 13:48:38,288 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:48:38,532 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:48:40,569 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:48:40,838 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:48:45,385 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:48:45,823 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:48:49,364 - production_monitor - DEBUG - System healthy - CPU: 3.8%, Memory: 77.8%, Positions: 0
2025-06-23 13:48:53,013 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:48:53,187 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:48:56,465 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:48:56,683 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:49:12,842 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:49:13,271 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:49:16,174 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:49:16,417 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:49:22,933 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:49:23,169 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:49:29,871 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:49:30,048 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:49:34,109 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:49:34,354 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:49:49,487 - production_monitor - DEBUG - System healthy - CPU: 4.2%, Memory: 77.8%, Positions: 0
2025-06-23 13:50:36,784 - unicode_test - INFO - [SUCCESS] Unicode-safe logging configured
2025-06-23 13:50:44,211 - unicode_test - INFO - [SUCCESS] Unicode-safe logging configured
2025-06-23 13:50:44,211 - main - INFO - Successfully configured Unicode-safe logging
2025-06-23 13:50:44,212 - main - INFO - Starting bot...
2025-06-23 13:50:44,213 - helius_rate_limiter - INFO - Helius Rate Limiter initialized: 50.0 RPS
2025-06-23 13:50:44,213 - main - INFO - [SUCCESS] BULLETPROOF SEPARATION: Analysis and Position Monitoring functions initialized
2025-06-23 13:50:44,213 - main - INFO - [SUCCESS] PERMANENT TRACKING: 0 analyzed, 0 skipped
2025-06-23 13:50:44,218 - pumpportal_trader - DEBUG - Using MAINNET RPC: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-23 13:50:44,218 - pumpportal_trader - INFO - Loaded transaction settings: {}
2025-06-23 13:50:44,219 - pumpportal_trader - INFO - Loaded configured default wallet: TEST1 with public key: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-23 13:50:44,219 - pumpportal_trader - INFO - PumpPortal Trader initialized for mainnet pump.fun trading
2025-06-23 13:50:44,219 - pumpportal_trader - INFO - Mainnet API URL: https://pumpportal.fun/api/trade-local
2025-06-23 13:50:44,220 - pumpportal_trader - INFO - [WARNING] Note: PumpPortal only supports mainnet
2025-06-23 13:50:44,220 - pumpportal_trader - INFO - RPC URL: https://mainnet.helius-rpc.com/?api-key=a4e2922a-0...
2025-06-23 13:50:44,220 - pumpportal_trader - INFO - Helius rate limiter: 50.0 RPS
2025-06-23 13:50:44,221 - unified_trader - INFO - Unified Trader initialized for mainnet real trading with adaptive slippage support
2025-06-23 13:50:44,222 - main - INFO - CRITICAL DEBUG: SIMPLE_PUMP_AVAILABLE = True
2025-06-23 13:50:44,222 - main - INFO - CRITICAL DEBUG: _fast_analysis_function = <function init_simple_pump_analyzer.<locals>.analyze_new_signal_only at 0x00000244625FFEC0>
2025-06-23 13:50:44,222 - main - INFO - CRITICAL DEBUG: _position_monitoring_function = <function init_simple_pump_analyzer.<locals>.monitor_position_only at 0x00000244625FFF60>
2025-06-23 13:50:44,222 - main - INFO - [SUCCESS] Fast analysis function connected to bot controller (NEW SIGNALS ONLY)
2025-06-23 13:50:44,223 - main - INFO - [SUCCESS] Position monitoring function connected to bot controller (BOUGHT POSITIONS ONLY)
2025-06-23 13:50:44,223 - main - INFO - [AIM] MULTIPLIER FILTERING: ENABLED - Will reject tokens with 5x+ gains (allowing up to 5x multiplier)
2025-06-23 13:50:44,223 - main - INFO - Initializing DEX adapters and sessions
2025-06-23 13:50:44,223 - main - INFO - DEX sessions initialized successfully
2025-06-23 13:50:44,224 - main - INFO - LLM integration is not available in this configuration
2025-06-23 13:50:44,224 - main - INFO - Registered direct signal callback for faster signal proyescessing
2025-06-23 13:50:44,224 - main - INFO - LLM integration is not available in this configuration
2025-06-23 13:50:44,225 - main - INFO - Initializing bot controller WITHOUT signal processing...
2025-06-23 13:50:46,589 - main - INFO - [SUCCESS] Bot controller initialized successfully WITHOUT signal processing
2025-06-23 13:50:46,589 - main - INFO - Started cache cleanup task
2025-06-23 13:50:46,604 - production_monitor - INFO - Production monitoring started
2025-06-23 13:50:46,605 - main - INFO - [SUCCESS] Production monitoring started
2025-06-23 13:50:46,607 - error_tracker - INFO - [SUCCESS] Error tracking enabled
2025-06-23 13:50:46,608 - main - INFO - [SUCCESS] Error tracking enabled
2025-06-23 13:50:46,609 - main - INFO - [SUCCESS] Trading metrics collector enabled
2025-06-23 13:50:46,610 - main - INFO - Starting CLI interface...
2025-06-23 13:50:46,615 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:50:46,779 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:50:46,785 - production_monitor - INFO - Production monitor started
2025-06-23 13:50:46,892 - production_monitor - DEBUG - System healthy - CPU: 4.8%, Memory: 77.9%, Positions: 0
2025-06-23 13:51:00,964 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 13:51:01,153 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=a4e2922a-0563-4031-ae24-c36398cc5852 HTTP/1.1" 200 None
2025-06-23 13:51:47,012 - production_monitor - DEBUG - System healthy - CPU: 8.3%, Memory: 79.8%, Positions: 0
2025-06-23 13:52:47,121 - production_monitor - DEBUG - System healthy - CPU: 17.0%, Memory: 79.7%, Positions: 0
2025-06-23 13:53:47,230 - production_monitor - DEBUG - System healthy - CPU: 6.3%, Memory: 81.8%, Positions: 0
2025-06-23 13:56:57,898 - unicode_test - INFO - [SUCCESS] Unicode-safe logging configured
2025-06-23 14:12:23,578 - unicode_test - INFO - [SUCCESS] Unicode-safe logging configured
2025-06-23 16:45:18,097 - unicode_test - INFO - [SUCCESS] Unicode-safe logging configured
2025-06-23 16:45:20,946 - unicode_test - INFO - [SUCCESS] Unicode-safe logging configured
2025-06-23 16:45:20,946 - main - INFO - Successfully configured Unicode-safe logging
2025-06-23 16:45:20,948 - main - INFO - Starting bot...
2025-06-23 16:45:20,950 - helius_rate_limiter - INFO - Helius Rate Limiter initialized: 50.0 RPS
2025-06-23 16:45:20,950 - main - INFO - [SUCCESS] BULLETPROOF SEPARATION: Analysis and Position Monitoring functions initialized
2025-06-23 16:45:20,950 - main - INFO - [SUCCESS] PERMANENT TRACKING: 0 analyzed, 0 skipped
2025-06-23 16:45:20,961 - pumpportal_trader - DEBUG - Using MAINNET RPC: https://mainnet.helius-rpc.com/?api-key=6ae4dea2-9...
2025-06-23 16:45:20,961 - pumpportal_trader - INFO - Loaded transaction settings: {}
2025-06-23 16:45:20,967 - pumpportal_trader - INFO - Loaded configured default wallet: TEST1 with public key: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-23 16:45:20,968 - pumpportal_trader - INFO - PumpPortal Trader initialized for mainnet pump.fun trading
2025-06-23 16:45:20,968 - pumpportal_trader - INFO - Mainnet API URL: https://pumpportal.fun/api/trade-local
2025-06-23 16:45:20,969 - pumpportal_trader - INFO - [WARNING] Note: PumpPortal only supports mainnet
2025-06-23 16:45:20,969 - pumpportal_trader - INFO - RPC URL: https://mainnet.helius-rpc.com/?api-key=6ae4dea2-9...
2025-06-23 16:45:20,969 - pumpportal_trader - INFO - Helius rate limiter: 50.0 RPS
2025-06-23 16:45:20,978 - unified_trader - INFO - Unified Trader initialized for mainnet real trading with adaptive slippage support
2025-06-23 16:45:20,980 - main - INFO - CRITICAL DEBUG: SIMPLE_PUMP_AVAILABLE = True
2025-06-23 16:45:20,981 - main - INFO - CRITICAL DEBUG: _fast_analysis_function = <function init_simple_pump_analyzer.<locals>.analyze_new_signal_only at 0x000001CD569E3380>
2025-06-23 16:45:20,981 - main - INFO - CRITICAL DEBUG: _position_monitoring_function = <function init_simple_pump_analyzer.<locals>.monitor_position_only at 0x000001CD569E3420>
2025-06-23 16:45:20,982 - main - INFO - [SUCCESS] Fast analysis function connected to bot controller (NEW SIGNALS ONLY)
2025-06-23 16:45:20,982 - main - INFO - [SUCCESS] Position monitoring function connected to bot controller (BOUGHT POSITIONS ONLY)
2025-06-23 16:45:20,983 - main - INFO - [AIM] MULTIPLIER FILTERING: ENABLED - Will reject tokens with 5x+ gains (allowing up to 5x multiplier)
2025-06-23 16:45:20,985 - main - INFO - Initializing DEX adapters and sessions
2025-06-23 16:45:20,986 - main - INFO - DEX sessions initialized successfully
2025-06-23 16:45:20,986 - main - INFO - LLM integration is not available in this configuration
2025-06-23 16:45:20,987 - main - INFO - Registered direct signal callback for faster signal proyescessing
2025-06-23 16:45:20,987 - main - INFO - LLM integration is not available in this configuration
2025-06-23 16:45:20,997 - main - INFO - Initializing bot controller WITHOUT signal processing...
2025-06-23 16:45:34,741 - main - INFO - [SUCCESS] Bot controller initialized successfully WITHOUT signal processing
2025-06-23 16:45:34,742 - main - INFO - Started cache cleanup task
2025-06-23 16:45:34,764 - production_monitor - INFO - Production monitoring started
2025-06-23 16:45:34,765 - main - INFO - [SUCCESS] Production monitoring started
2025-06-23 16:45:34,772 - error_tracker - INFO - [SUCCESS] Error tracking enabled
2025-06-23 16:45:34,773 - main - INFO - [SUCCESS] Error tracking enabled
2025-06-23 16:45:34,781 - main - INFO - [SUCCESS] Trading metrics collector enabled
2025-06-23 16:45:34,781 - main - INFO - Starting CLI interface...
2025-06-23 16:45:34,791 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:45:35,972 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:45:36,021 - production_monitor - INFO - Production monitor started
2025-06-23 16:45:36,128 - production_monitor - WARNING - PRODUCTION ALERTS: HIGH MEMORY: 87.9%
2025-06-23 16:45:40,152 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:45:41,268 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:45:45,903 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:45:46,874 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:45:53,134 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:45:54,289 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:45:55,591 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:45:56,753 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:45:58,684 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:45:59,576 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:46:05,839 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:46:06,923 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:47:10,351 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:47:11,846 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:47:11,997 - production_monitor - WARNING - PRODUCTION ALERTS: HIGH MEMORY: 85.9%
2025-06-23 16:47:25,770 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:47:26,724 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:47:34,570 - unicode_test - INFO - [SUCCESS] Unicode-safe logging configured
2025-06-23 16:47:39,872 - unicode_test - INFO - [SUCCESS] Unicode-safe logging configured
2025-06-23 16:47:39,872 - main - INFO - Successfully configured Unicode-safe logging
2025-06-23 16:47:39,873 - main - INFO - Starting bot...
2025-06-23 16:47:39,875 - helius_rate_limiter - INFO - Helius Rate Limiter initialized: 50.0 RPS
2025-06-23 16:47:39,875 - main - INFO - [SUCCESS] BULLETPROOF SEPARATION: Analysis and Position Monitoring functions initialized
2025-06-23 16:47:39,875 - main - INFO - [SUCCESS] PERMANENT TRACKING: 0 analyzed, 0 skipped
2025-06-23 16:47:39,889 - pumpportal_trader - DEBUG - Using MAINNET RPC: https://mainnet.helius-rpc.com/?api-key=6ae4dea2-9...
2025-06-23 16:47:39,889 - pumpportal_trader - INFO - Loaded transaction settings: {}
2025-06-23 16:47:39,892 - pumpportal_trader - INFO - Loaded configured default wallet: TEST1 with public key: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-23 16:47:39,892 - pumpportal_trader - INFO - PumpPortal Trader initialized for mainnet pump.fun trading
2025-06-23 16:47:39,892 - pumpportal_trader - INFO - Mainnet API URL: https://pumpportal.fun/api/trade-local
2025-06-23 16:47:39,893 - pumpportal_trader - INFO - [WARNING] Note: PumpPortal only supports mainnet
2025-06-23 16:47:39,893 - pumpportal_trader - INFO - RPC URL: https://mainnet.helius-rpc.com/?api-key=6ae4dea2-9...
2025-06-23 16:47:39,894 - pumpportal_trader - INFO - Helius rate limiter: 50.0 RPS
2025-06-23 16:47:39,896 - unified_trader - INFO - Unified Trader initialized for mainnet real trading with adaptive slippage support
2025-06-23 16:47:39,899 - main - INFO - CRITICAL DEBUG: SIMPLE_PUMP_AVAILABLE = True
2025-06-23 16:47:39,899 - main - INFO - CRITICAL DEBUG: _fast_analysis_function = <function init_simple_pump_analyzer.<locals>.analyze_new_signal_only at 0x000001C4F9BEBA60>
2025-06-23 16:47:39,899 - main - INFO - CRITICAL DEBUG: _position_monitoring_function = <function init_simple_pump_analyzer.<locals>.monitor_position_only at 0x000001C4F9BEBB00>
2025-06-23 16:47:39,900 - main - INFO - [SUCCESS] Fast analysis function connected to bot controller (NEW SIGNALS ONLY)
2025-06-23 16:47:39,900 - main - INFO - [SUCCESS] Position monitoring function connected to bot controller (BOUGHT POSITIONS ONLY)
2025-06-23 16:47:39,901 - main - INFO - [AIM] MULTIPLIER FILTERING: ENABLED - Will reject tokens with 5x+ gains (allowing up to 5x multiplier)
2025-06-23 16:47:39,901 - main - INFO - Initializing DEX adapters and sessions
2025-06-23 16:47:39,902 - main - INFO - DEX sessions initialized successfully
2025-06-23 16:47:39,902 - main - INFO - LLM integration is not available in this configuration
2025-06-23 16:47:39,902 - main - INFO - Registered direct signal callback for faster signal proyescessing
2025-06-23 16:47:39,903 - main - INFO - LLM integration is not available in this configuration
2025-06-23 16:47:39,904 - main - INFO - Initializing bot controller WITHOUT signal processing...
2025-06-23 16:47:40,876 - main - INFO - [SUCCESS] Bot controller initialized successfully WITHOUT signal processing
2025-06-23 16:47:40,877 - main - INFO - Started cache cleanup task
2025-06-23 16:47:40,899 - production_monitor - INFO - Production monitoring started
2025-06-23 16:47:40,899 - main - INFO - [SUCCESS] Production monitoring started
2025-06-23 16:47:40,901 - error_tracker - INFO - [SUCCESS] Error tracking enabled
2025-06-23 16:47:40,902 - main - INFO - [SUCCESS] Error tracking enabled
2025-06-23 16:47:40,904 - main - INFO - [SUCCESS] Trading metrics collector enabled
2025-06-23 16:47:40,906 - main - INFO - Starting CLI interface...
2025-06-23 16:47:40,916 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:47:42,100 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:47:42,143 - production_monitor - INFO - Production monitor started
2025-06-23 16:47:42,251 - production_monitor - WARNING - PRODUCTION ALERTS: HIGH MEMORY: 86.5%
2025-06-23 16:47:43,369 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:47:44,466 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:47:46,615 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:47:47,688 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:47:50,712 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:47:51,914 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:47:57,376 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:47:58,504 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:48:42,373 - production_monitor - WARNING - PRODUCTION ALERTS: HIGH MEMORY: 87.8%
2025-06-23 16:48:54,350 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:48:55,541 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:48:57,439 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:48:58,517 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:49:05,135 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:49:06,346 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:49:16,783 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:49:17,871 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:49:42,496 - production_monitor - WARNING - PRODUCTION ALERTS: HIGH MEMORY: 89.2%
2025-06-23 16:49:42,696 - pumpportal_trader - DEBUG - Using MAINNET RPC: https://mainnet.helius-rpc.com/?api-key=6ae4dea2-9...
2025-06-23 16:49:42,696 - pumpportal_trader - INFO - Loaded transaction settings: {}
2025-06-23 16:49:42,698 - pumpportal_trader - INFO - Loaded configured default wallet: TEST1 with public key: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-23 16:49:42,698 - pumpportal_trader - INFO - PumpPortal Trader initialized for mainnet pump.fun trading
2025-06-23 16:49:42,698 - pumpportal_trader - INFO - Mainnet API URL: https://pumpportal.fun/api/trade-local
2025-06-23 16:49:42,698 - pumpportal_trader - INFO - [WARNING] Note: PumpPortal only supports mainnet
2025-06-23 16:49:42,699 - pumpportal_trader - INFO - RPC URL: https://mainnet.helius-rpc.com/?api-key=6ae4dea2-9...
2025-06-23 16:49:42,699 - pumpportal_trader - INFO - Helius rate limiter: 50.0 RPS
2025-06-23 16:49:42,699 - unified_trader - INFO - [SUCCESS] PumpPortal trader initialized for mainnet
2025-06-23 16:49:42,699 - unified_trader - INFO - ℹ️ Helius trader disabled - devnet support removed
2025-06-23 16:49:42,700 - unified_trader - INFO - [SUCCESS] PumpPortal trader initialized successfully (mainnet only)
2025-06-23 16:49:42,702 - pumpportal_trader - DEBUG - Using MAINNET RPC: https://mainnet.helius-rpc.com/?api-key=6ae4dea2-9...
2025-06-23 16:49:42,702 - pumpportal_trader - INFO - [REFRESH] PumpPortal trader set to MAINNET mode
2025-06-23 16:49:42,702 - pumpportal_trader - INFO - [NETWORK] Updated RPC URL: https://mainnet.helius-rpc.com/?api-key=6ae4dea2-9...
2025-06-23 16:49:42,702 - pumpportal_trader - INFO - 🔗 API URL: https://pumpportal.fun/api/trade-local
2025-06-23 16:49:42,702 - unified_trader - INFO - [LAUNCH] Confirmed MAINNET mode - using PumpPortal for pump.fun
2025-06-23 16:49:45,236 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:49:46,479 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:49:56,601 - main - INFO - [FAST] Analyzing token: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:49:56,605 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.dexscreener.com:443
2025-06-23 16:49:56,606 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:49:56,606 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): api.coingecko.com:443
2025-06-23 16:49:57,711 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:49:57,728 - main - DEBUG - Token supply success: 1,000,000,000
2025-06-23 16:49:57,761 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump HTTP/1.1" 200 None
2025-06-23 16:49:57,766 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 16:49:58,395 - urllib3.connectionpool - DEBUG - https://api.coingecko.com:443 "GET /api/v3/simple/price?ids=solana&vs_currencies=usd HTTP/1.1" 200 None
2025-06-23 16:49:58,405 - main - INFO - Fetched fresh SOL price: $133.72 (attempt 1)
2025-06-23 16:49:58,407 - main - INFO - Fetched data - SOL price: $133.72, Supply: 1,000,000,000
2025-06-23 16:49:58,408 - main - INFO - DexScreener data available - Price: $1.111e-05, Volume 24h: $20,629
2025-06-23 16:49:58,409 - main - INFO - [SCAN] Looking for pump.fun pool for CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:49:58,414 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:49:59,740 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:49:59,745 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:00,732 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:00,751 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:01,784 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:01,796 - main - DEBUG - Pool balance success: 30.367833 SOL
2025-06-23 16:50:01,796 - main - INFO - [SUCCESS] Found pool address: BP9nFjp4cZ6JQYGtgidS3TXoo7ovE6mXSN5UdMmCqopb
2025-06-23 16:50:01,798 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:03,019 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:03,037 - main - DEBUG - Pool balance success: 29.224553 SOL
2025-06-23 16:50:03,037 - main - INFO - [MONEY] Pool SOL balance: 29.224552996
2025-06-23 16:50:03,038 - main - INFO - [CHART] Calculated liquidity: $7,620 (SOL: 29.224552996 * $133.72 * 1.95)
2025-06-23 16:50:03,038 - main - INFO - Found pool BP9nFjp4... with 29.224553 SOL
2025-06-23 16:50:03,038 - main - INFO - Using calculated liquidity: $7,620 (x1.95)
2025-06-23 16:50:03,040 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:04,204 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:04,216 - main - DEBUG - Holders success: 20 holders
2025-06-23 16:50:04,217 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:05,257 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:05,276 - main - INFO - [FAST] Analysis completed in 8.67s
2025-06-23 16:50:05,277 - main - INFO - [SCAN] MULTIPLIER CHECK ENABLED: Checking CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump for high multipliers (3x+)
2025-06-23 16:50:05,277 - main - INFO - [AIM] GMGN SIGNAL DETECTED: Allowing high multipliers for CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:05,278 - main - INFO - [AIM] GMGN signals are exempt from multiplier restrictions
2025-06-23 16:50:05,281 - main - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:05,282 - main - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $7,620, MC: $11,118, Whale: 21.6%
2025-06-23 16:50:05,282 - main - INFO - [SUCCESS] Fast analysis result: Price=$0.00001111, MC=$11,118, Liq=$7,620
2025-06-23 16:50:05,283 - main - DEBUG - PERMANENT STATE: 1 total, 0 skipped
2025-06-23 16:50:05,283 - main - DEBUG - Permanent decision recorded for CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:05,295 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:06,498 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:06,506 - pumpportal_trader - INFO - [DYNAMIC FEE] Network congestion: LOW (1568 tx/slot)
2025-06-23 16:50:06,506 - pumpportal_trader - INFO - [DYNAMIC FEE] Priority fee: 0.000100 → 0.000150 SOL (1.5x)
2025-06-23 16:50:06,508 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:07,706 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:07,716 - pumpportal_trader - INFO - [DYNAMIC FEE] Network congestion: LOW (1573 tx/slot)
2025-06-23 16:50:07,716 - pumpportal_trader - INFO - [DYNAMIC FEE] Priority fee: 0.000100 → 0.000150 SOL (1.5x)
2025-06-23 16:50:07,717 - pumpportal_trader - INFO - [MONEY] EXTERNAL FEES: Buy tip: 0.0002 SOL, Gas: 0.0002 SOL, Handling: 0.0000 SOL, Platform: 0.0002 SOL
2025-06-23 16:50:07,717 - pumpportal_trader - INFO - [MONEY] TOTAL EXTERNAL FEES: 0.0009 SOL
2025-06-23 16:50:07,718 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:08,687 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:08,706 - pumpportal_trader - DEBUG - Wallet balance: 0.131001 SOL
2025-06-23 16:50:08,706 - pumpportal_trader - INFO - [GREEN] REAL BUY: 0.0400 SOL of CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump (slippage: 16.0%) + 0.0009 SOL fees - Balance: 0.1310 SOL
2025-06-23 16:50:08,708 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:09,897 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:09,916 - pumpportal_trader - INFO - [DYNAMIC FEE] Network congestion: LOW (1572 tx/slot)
2025-06-23 16:50:09,916 - pumpportal_trader - INFO - [DYNAMIC FEE] Priority fee: 0.000100 → 0.000150 SOL (1.5x)
2025-06-23 16:50:09,917 - pumpportal_trader - DEBUG - Priority fee: 0.00015000 SOL (150000 lamports)
2025-06-23 16:50:09,917 - pumpportal_trader - INFO - [SCAN] DETAILED PumpPortal payload:
2025-06-23 16:50:09,917 - pumpportal_trader - INFO -    action: buy
2025-06-23 16:50:09,917 - pumpportal_trader - INFO -    mint: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:09,918 - pumpportal_trader - INFO -    amount: 0.04 (original: 0.04)
2025-06-23 16:50:09,918 - pumpportal_trader - INFO -    denominatedInSol: true
2025-06-23 16:50:09,918 - pumpportal_trader - INFO -    slippage: 16
2025-06-23 16:50:09,919 - pumpportal_trader - INFO -    priorityFee: 0.00015000000000000001 SOL
2025-06-23 16:50:09,919 - pumpportal_trader - INFO -    publicKey: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-23 16:50:09,919 - pumpportal_trader - INFO - [NETWORK] Using PumpPortal API: https://pumpportal.fun/api/trade-local (Network: MAINNET)
2025-06-23 16:50:09,921 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): pumpportal.fun:443
2025-06-23 16:50:11,508 - urllib3.connectionpool - DEBUG - https://pumpportal.fun:443 "POST /api/trade-local HTTP/1.1" 200 688
2025-06-23 16:50:11,509 - pumpportal_trader - INFO - [SIGNAL] PumpPortal API Response: 200
2025-06-23 16:50:11,523 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:12,613 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:12,627 - pumpportal_trader - INFO - Transaction sent successfully on attempt 1: 3EgqVVG4ts8xhb6fastxmsQw9f5tdsF4AHNmxv5Xv5h1PAbtpVMxjnUV3LNgoqunod2bgX5hmW6zCxKbQxdiZqus
2025-06-23 16:50:12,628 - pumpportal_trader - INFO - [SUCCESS] Transaction sent successfully: 3EgqVVG4ts8xhb6fastxmsQw9f5tdsF4AHNmxv5Xv5h1PAbtpVMxjnUV3LNgoqunod2bgX5hmW6zCxKbQxdiZqus
2025-06-23 16:50:12,629 - pumpportal_trader - INFO - [SCAN] CRITICAL FIX: Waiting for blockchain verification before confirming success...
2025-06-23 16:50:12,631 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:14,062 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:14,076 - pumpportal_trader - INFO - [SUCCESS] Transaction VERIFIED successful: 3EgqVVG4ts8xhb6fastxmsQw9f5tdsF4AHNmxv5Xv5h1PAbtpVMxjnUV3LNgoqunod2bgX5hmW6zCxKbQxdiZqus
2025-06-23 16:50:14,077 - pumpportal_trader - INFO - [SUCCESS] VERIFIED: Transaction confirmed on blockchain: 3EgqVVG4ts8xhb6fastxmsQw9f5tdsF4AHNmxv5Xv5h1PAbtpVMxjnUV3LNgoqunod2bgX5hmW6zCxKbQxdiZqus
2025-06-23 16:50:14,079 - pumpportal_trader - INFO - [SUCCESS] Buy successful: 3EgqVVG4ts8xhb6fastxmsQw9f5tdsF4AHNmxv5Xv5h1PAbtpVMxjnUV3LNgoqunod2bgX5hmW6zCxKbQxdiZqus (Total cost: 0.0409 SOL)
2025-06-23 16:50:14,102 - main - DEBUG - POSITION MONITORING: Getting price update for CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:14,102 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:14,131 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump HTTP/1.1" 200 None
2025-06-23 16:50:14,145 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 16:50:14,145 - main - DEBUG - POSITION MONITORING: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump - Price: $0.00001111, Liquidity: $0
2025-06-23 16:50:14,701 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:15,974 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:16,012 - main - DEBUG - POSITION MONITORING: Getting price update for CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:16,012 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:16,080 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump HTTP/1.1" 200 None
2025-06-23 16:50:16,095 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 16:50:16,095 - main - DEBUG - POSITION MONITORING: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump - Price: $0.00001111, Liquidity: $0
2025-06-23 16:50:17,402 - main - DEBUG - POSITION MONITORING: Getting price update for CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:17,403 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:17,440 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump HTTP/1.1" 200 None
2025-06-23 16:50:17,455 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 16:50:17,455 - main - DEBUG - POSITION MONITORING: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump - Price: $0.00001111, Liquidity: $0
2025-06-23 16:50:18,762 - main - DEBUG - POSITION MONITORING: Getting price update for CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:18,762 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:18,803 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump HTTP/1.1" 200 None
2025-06-23 16:50:18,817 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 16:50:18,817 - main - DEBUG - POSITION MONITORING: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump - Price: $0.00001111, Liquidity: $0
2025-06-23 16:50:20,126 - main - DEBUG - POSITION MONITORING: Getting price update for CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:20,126 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:20,158 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump HTTP/1.1" 200 None
2025-06-23 16:50:20,175 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 16:50:20,175 - main - DEBUG - POSITION MONITORING: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump - Price: $0.00001111, Liquidity: $0
2025-06-23 16:50:21,496 - main - DEBUG - POSITION MONITORING: Getting price update for CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:21,496 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:21,533 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump HTTP/1.1" 200 None
2025-06-23 16:50:21,545 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 16:50:21,545 - main - DEBUG - POSITION MONITORING: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump - Price: $0.00001111, Liquidity: $0
2025-06-23 16:50:22,855 - main - DEBUG - POSITION MONITORING: Getting price update for CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:22,856 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:22,889 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump HTTP/1.1" 200 None
2025-06-23 16:50:22,906 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 16:50:22,907 - main - DEBUG - POSITION MONITORING: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump - Price: $0.00001111, Liquidity: $0
2025-06-23 16:50:24,234 - main - DEBUG - POSITION MONITORING: Getting price update for CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:24,234 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:24,268 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump HTTP/1.1" 200 None
2025-06-23 16:50:24,285 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 16:50:24,286 - main - DEBUG - POSITION MONITORING: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump - Price: $0.00001111, Liquidity: $0
2025-06-23 16:50:25,603 - main - DEBUG - POSITION MONITORING: Getting price update for CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:25,603 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:25,637 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump HTTP/1.1" 200 None
2025-06-23 16:50:25,655 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 16:50:25,655 - main - DEBUG - POSITION MONITORING: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump - Price: $0.00001111, Liquidity: $0
2025-06-23 16:50:26,975 - main - DEBUG - POSITION MONITORING: Getting price update for CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:26,975 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:27,007 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump HTTP/1.1" 200 None
2025-06-23 16:50:27,025 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 16:50:27,026 - main - DEBUG - POSITION MONITORING: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump - Price: $0.00001111, Liquidity: $0
2025-06-23 16:50:28,353 - main - DEBUG - POSITION MONITORING: Getting price update for CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:28,354 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:28,649 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump HTTP/1.1" 200 None
2025-06-23 16:50:28,665 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 16:50:28,665 - main - DEBUG - POSITION MONITORING: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump - Price: $0.00001389, Liquidity: $0
2025-06-23 16:50:28,682 - pumpportal_trader - INFO - [RED] BULLETPROOF SELL ATTEMPT 1/2: 3600.3600360036003 tokens of CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:28,685 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:29,602 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:29,617 - pumpportal_trader - INFO - [DYNAMIC FEE] Network congestion: LOW (1575 tx/slot)
2025-06-23 16:50:29,618 - pumpportal_trader - INFO - [DYNAMIC FEE] Priority fee: 0.000100 → 0.000150 SOL (1.5x)
2025-06-23 16:50:29,618 - pumpportal_trader - INFO - [MONEY] BULLETPROOF FEES: Gas: 0.0002 SOL, Handling: 0.0%, Platform: 0.5%
2025-06-23 16:50:29,619 - pumpportal_trader - DEBUG - [SCAN] Checking token balance for CdPWwNAE... (attempt 1/3)
2025-06-23 16:50:29,620 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:31,049 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:31,071 - pumpportal_trader - DEBUG - [SCAN] Found 1 token accounts for CdPWwNAE...
2025-06-23 16:50:31,071 - pumpportal_trader - INFO - [SUCCESS] Token balance found: 368173.815652 tokens (raw: ************, decimals: 6)
2025-06-23 16:50:31,072 - pumpportal_trader - INFO - [SCAN] ORIGINAL BALANCE: 368173.815652 tokens
2025-06-23 16:50:31,072 - pumpportal_trader - INFO - [SCAN] BULLETPROOF: Current token balance: 368173.815652 tokens
2025-06-23 16:50:31,073 - pumpportal_trader - INFO - [AIM] OVERRIDE: Ignoring token_amount 3600.360036 - selling 100% of wallet
2025-06-23 16:50:31,074 - pumpportal_trader - INFO - [AIM] OVERRIDE: Main bot wanted to sell 3600.360036 but wallet has 368173.815652
2025-06-23 16:50:31,074 - pumpportal_trader - INFO - [AIM] BULLETPROOF STRATEGY: Sell ALL tokens - 100% (368173.815652 tokens)
2025-06-23 16:50:31,075 - pumpportal_trader - INFO - [AIM] REASON: ALWAYS sell complete wallet balance regardless of input parameters
2025-06-23 16:50:31,076 - pumpportal_trader - INFO - [SCAN] PRE-SELL BALANCE STORED: 368173.815652 tokens
2025-06-23 16:50:31,080 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:32,144 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:32,158 - pumpportal_trader - INFO - [DYNAMIC FEE] Network congestion: LOW (1574 tx/slot)
2025-06-23 16:50:32,159 - pumpportal_trader - INFO - [DYNAMIC FEE] Priority fee: 0.000100 → 0.000150 SOL (1.5x)
2025-06-23 16:50:32,159 - pumpportal_trader - DEBUG - Priority fee: 0.00015000 SOL (150000 lamports)
2025-06-23 16:50:32,159 - pumpportal_trader - INFO - [FIX] CRITICAL FIX: Keeping percentage as string: '100%' (PumpPortal requires string format)
2025-06-23 16:50:32,160 - pumpportal_trader - INFO - [SCAN] DETAILED PumpPortal payload:
2025-06-23 16:50:32,160 - pumpportal_trader - INFO -    action: sell
2025-06-23 16:50:32,161 - pumpportal_trader - INFO -    mint: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:32,161 - pumpportal_trader - INFO -    amount: 100% (original: 100%)
2025-06-23 16:50:32,162 - pumpportal_trader - INFO -    denominatedInSol: false
2025-06-23 16:50:32,162 - pumpportal_trader - INFO -    slippage: 24
2025-06-23 16:50:32,163 - pumpportal_trader - INFO -    priorityFee: 0.00015000000000000001 SOL
2025-06-23 16:50:32,164 - pumpportal_trader - INFO -    publicKey: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-23 16:50:32,166 - pumpportal_trader - INFO - [NETWORK] Using PumpPortal API: https://pumpportal.fun/api/trade-local (Network: MAINNET)
2025-06-23 16:50:32,169 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): pumpportal.fun:443
2025-06-23 16:50:33,782 - urllib3.connectionpool - DEBUG - https://pumpportal.fun:443 "POST /api/trade-local HTTP/1.1" 200 696
2025-06-23 16:50:33,783 - pumpportal_trader - INFO - [SIGNAL] PumpPortal API Response: 200
2025-06-23 16:50:33,786 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:34,784 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:34,800 - pumpportal_trader - INFO - Transaction sent successfully on attempt 1: 5Z6fbGRRV1pjceVhVya5c8rCyrggUfnoNL6cXwxx5tovzrBa7GnHMXi8RBGYTgdATzjJwBdfqDLJgsyt93hP6oud
2025-06-23 16:50:34,801 - pumpportal_trader - INFO - [SUCCESS] Transaction sent successfully: 5Z6fbGRRV1pjceVhVya5c8rCyrggUfnoNL6cXwxx5tovzrBa7GnHMXi8RBGYTgdATzjJwBdfqDLJgsyt93hP6oud
2025-06-23 16:50:34,801 - pumpportal_trader - INFO - [SCAN] CRITICAL FIX: Waiting for blockchain verification before confirming success...
2025-06-23 16:50:34,805 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:36,255 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:36,257 - pumpportal_trader - INFO - [SUCCESS] Transaction VERIFIED successful: 5Z6fbGRRV1pjceVhVya5c8rCyrggUfnoNL6cXwxx5tovzrBa7GnHMXi8RBGYTgdATzjJwBdfqDLJgsyt93hP6oud
2025-06-23 16:50:36,257 - pumpportal_trader - INFO - [SUCCESS] VERIFIED: Transaction confirmed on blockchain: 5Z6fbGRRV1pjceVhVya5c8rCyrggUfnoNL6cXwxx5tovzrBa7GnHMXi8RBGYTgdATzjJwBdfqDLJgsyt93hP6oud
2025-06-23 16:50:36,259 - pumpportal_trader - INFO - [SUCCESS] BULLETPROOF SELL SUCCESS on attempt 1
2025-06-23 16:50:36,259 - pumpportal_trader - INFO - [SUCCESS] Sell successful: 5Z6fbGRRV1pjceVhVya5c8rCyrggUfnoNL6cXwxx5tovzrBa7GnHMXi8RBGYTgdATzjJwBdfqDLJgsyt93hP6oud (Gas cost: 0.0002 SOL)
2025-06-23 16:50:36,260 - pumpportal_trader - INFO - [SCAN] CRITICAL DEBUG: Transaction verified on blockchain: True
2025-06-23 16:50:36,260 - pumpportal_trader - INFO - 🔗 Solscan link: https://solscan.io/tx/5Z6fbGRRV1pjceVhVya5c8rCyrggUfnoNL6cXwxx5tovzrBa7GnHMXi8RBGYTgdATzjJwBdfqDLJgsyt93hP6oud
2025-06-23 16:50:36,261 - pumpportal_trader - INFO - [SCAN] Waiting 3 seconds for blockchain state to update...
2025-06-23 16:50:39,270 - pumpportal_trader - DEBUG - [SCAN] Checking token balance for CdPWwNAE... (attempt 1/3)
2025-06-23 16:50:39,271 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:40,339 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:40,359 - pumpportal_trader - DEBUG - [SCAN] Found 1 token accounts for CdPWwNAE...
2025-06-23 16:50:40,359 - pumpportal_trader - INFO - [SUCCESS] Token balance found: 368173.815652 tokens (raw: ************, decimals: 6)
2025-06-23 16:50:40,359 - pumpportal_trader - INFO - [SCAN] POST-SELL TOKEN BALANCE CHECK: 368173.815652 tokens remaining
2025-06-23 16:50:40,360 - pumpportal_trader - WARNING - [SCAN] LARGE WALLET BALANCE: 368173.815652 tokens (suggests multiple buys)
2025-06-23 16:50:40,360 - pumpportal_trader - WARNING - [SCAN] Any reduction in balance will be considered a successful sell
2025-06-23 16:50:40,360 - pumpportal_trader - INFO - [SCAN] SELL VERIFICATION: Pre-sell: 368173.815652, Post-sell: 368173.815652, Sold: 0.000000
2025-06-23 16:50:40,361 - pumpportal_trader - ERROR - [ERROR] CRITICAL: SELL FAILED - No tokens sold! Balance: 368173.815652
2025-06-23 16:50:40,362 - pumpportal_trader - ERROR - [ERROR] This indicates the transaction failed or was fake!
2025-06-23 16:50:40,365 - utils - ERROR - sell_error: Failed PumpPortal sell command for CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump [Context: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump]
2025-06-23 16:50:41,686 - main - DEBUG - POSITION MONITORING: Getting price update for CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:41,687 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:41,732 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump HTTP/1.1" 200 None
2025-06-23 16:50:41,745 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 16:50:41,746 - main - DEBUG - POSITION MONITORING: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump - Price: $0.00001389, Liquidity: $0
2025-06-23 16:50:41,761 - pumpportal_trader - INFO - [RED] BULLETPROOF SELL ATTEMPT 1/2: 3600.3600360036003 tokens of CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:41,763 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:42,938 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:42,959 - pumpportal_trader - INFO - [DYNAMIC FEE] Network congestion: LOW (1572 tx/slot)
2025-06-23 16:50:42,959 - pumpportal_trader - INFO - [DYNAMIC FEE] Priority fee: 0.000100 → 0.000150 SOL (1.5x)
2025-06-23 16:50:42,960 - pumpportal_trader - INFO - [MONEY] BULLETPROOF FEES: Gas: 0.0002 SOL, Handling: 0.0%, Platform: 0.5%
2025-06-23 16:50:42,961 - pumpportal_trader - DEBUG - [SCAN] Checking token balance for CdPWwNAE... (attempt 1/3)
2025-06-23 16:50:42,965 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:44,006 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:44,010 - pumpportal_trader - DEBUG - [SCAN] Found 1 token accounts for CdPWwNAE...
2025-06-23 16:50:44,010 - pumpportal_trader - INFO - [SUCCESS] Token balance found: 368173.815652 tokens (raw: ************, decimals: 6)
2025-06-23 16:50:44,011 - pumpportal_trader - INFO - [SCAN] ORIGINAL BALANCE: 368173.815652 tokens
2025-06-23 16:50:44,012 - pumpportal_trader - INFO - [SCAN] BULLETPROOF: Current token balance: 368173.815652 tokens
2025-06-23 16:50:44,012 - pumpportal_trader - INFO - [AIM] OVERRIDE: Ignoring token_amount 3600.360036 - selling 100% of wallet
2025-06-23 16:50:44,016 - pumpportal_trader - INFO - [AIM] OVERRIDE: Main bot wanted to sell 3600.360036 but wallet has 368173.815652
2025-06-23 16:50:44,018 - pumpportal_trader - INFO - [AIM] BULLETPROOF STRATEGY: Sell ALL tokens - 100% (368173.815652 tokens)
2025-06-23 16:50:44,019 - pumpportal_trader - INFO - [AIM] REASON: ALWAYS sell complete wallet balance regardless of input parameters
2025-06-23 16:50:44,019 - pumpportal_trader - INFO - [SCAN] PRE-SELL BALANCE STORED: 368173.815652 tokens
2025-06-23 16:50:44,023 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:45,283 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:45,300 - pumpportal_trader - INFO - [DYNAMIC FEE] Network congestion: LOW (1572 tx/slot)
2025-06-23 16:50:45,300 - pumpportal_trader - INFO - [DYNAMIC FEE] Priority fee: 0.000100 → 0.000150 SOL (1.5x)
2025-06-23 16:50:45,301 - pumpportal_trader - DEBUG - Priority fee: 0.00015000 SOL (150000 lamports)
2025-06-23 16:50:45,301 - pumpportal_trader - INFO - [FIX] CRITICAL FIX: Keeping percentage as string: '100%' (PumpPortal requires string format)
2025-06-23 16:50:45,302 - pumpportal_trader - INFO - [SCAN] DETAILED PumpPortal payload:
2025-06-23 16:50:45,302 - pumpportal_trader - INFO -    action: sell
2025-06-23 16:50:45,303 - pumpportal_trader - INFO -    mint: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:45,303 - pumpportal_trader - INFO -    amount: 100% (original: 100%)
2025-06-23 16:50:45,304 - pumpportal_trader - INFO -    denominatedInSol: false
2025-06-23 16:50:45,304 - pumpportal_trader - INFO -    slippage: 24
2025-06-23 16:50:45,305 - pumpportal_trader - INFO -    priorityFee: 0.00015000000000000001 SOL
2025-06-23 16:50:45,305 - pumpportal_trader - INFO -    publicKey: 75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA
2025-06-23 16:50:45,305 - pumpportal_trader - INFO - [NETWORK] Using PumpPortal API: https://pumpportal.fun/api/trade-local (Network: MAINNET)
2025-06-23 16:50:45,309 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): pumpportal.fun:443
2025-06-23 16:50:47,913 - urllib3.connectionpool - DEBUG - https://pumpportal.fun:443 "POST /api/trade-local HTTP/1.1" 200 696
2025-06-23 16:50:47,914 - pumpportal_trader - INFO - [SIGNAL] PumpPortal API Response: 200
2025-06-23 16:50:47,918 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:48,885 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:48,899 - pumpportal_trader - INFO - Transaction sent successfully on attempt 1: foKu9kwU5DGiaMYvPhxkWrfNb9QKsqzpwbwdaFcWnM7SrD5DSfqMQo7aPbgU8sM9Yd5NnZ46oFL6E7EHnj3c9kF
2025-06-23 16:50:48,900 - pumpportal_trader - INFO - [SUCCESS] Transaction sent successfully: foKu9kwU5DGiaMYvPhxkWrfNb9QKsqzpwbwdaFcWnM7SrD5DSfqMQo7aPbgU8sM9Yd5NnZ46oFL6E7EHnj3c9kF
2025-06-23 16:50:48,901 - pumpportal_trader - INFO - [SCAN] CRITICAL FIX: Waiting for blockchain verification before confirming success...
2025-06-23 16:50:48,904 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:50,160 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:50,177 - pumpportal_trader - ERROR - [ERROR] Transaction FAILED on blockchain: foKu9kwU5DGiaMYvPhxkWrfNb9QKsqzpwbwdaFcWnM7SrD5DSfqMQo7aPbgU8sM9Yd5NnZ46oFL6E7EHnj3c9kF - {'InstructionError': [3, {'Custom': 6022}]}
2025-06-23 16:50:50,178 - pumpportal_trader - ERROR - [ERROR] CRITICAL: Transaction FAILED verification on blockchain: foKu9kwU5DGiaMYvPhxkWrfNb9QKsqzpwbwdaFcWnM7SrD5DSfqMQo7aPbgU8sM9Yd5NnZ46oFL6E7EHnj3c9kF
2025-06-23 16:50:50,182 - pumpportal_trader - WARNING - [WARNING] SELL ATTEMPT 1 FAILED: Transaction failed blockchain verification
2025-06-23 16:50:50,182 - pumpportal_trader - INFO - [REFRESH] Will retry with higher slippage in 3 seconds...
2025-06-23 16:50:50,288 - production_monitor - WARNING - PRODUCTION ALERTS: HIGH MEMORY: 87.1%
2025-06-23 16:50:53,191 - pumpportal_trader - INFO - [RED] BULLETPROOF SELL ATTEMPT 2/2: 3600.3600360036003 tokens of CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:53,193 - pumpportal_trader - INFO - [REFRESH] RETRY 2: Increasing slippage to 29.0% for better success rate
2025-06-23 16:50:53,199 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:54,392 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:54,409 - pumpportal_trader - INFO - [DYNAMIC FEE] Network congestion: LOW (1573 tx/slot)
2025-06-23 16:50:54,409 - pumpportal_trader - INFO - [DYNAMIC FEE] Priority fee: 0.000100 → 0.000150 SOL (1.5x)
2025-06-23 16:50:54,410 - pumpportal_trader - INFO - [MONEY] BULLETPROOF FEES: Gas: 0.0002 SOL, Handling: 0.0%, Platform: 0.5%
2025-06-23 16:50:54,411 - pumpportal_trader - DEBUG - [SCAN] Checking token balance for CdPWwNAE... (attempt 1/3)
2025-06-23 16:50:54,414 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:55,583 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:55,600 - pumpportal_trader - DEBUG - [SCAN] Found 0 token accounts for CdPWwNAE...
2025-06-23 16:50:55,600 - pumpportal_trader - DEBUG - [SCAN] No token accounts found for CdPWwNAE... - wallet has 0 balance
2025-06-23 16:50:55,600 - pumpportal_trader - INFO - [AIM] PREVIOUS SELL SUCCEEDED! Balance went from 368173.815652 to 0 tokens
2025-06-23 16:50:55,601 - pumpportal_trader - INFO - [SUCCESS] SELL SUCCESS DETECTED: All tokens sold in previous attempt
2025-06-23 16:50:55,819 - main - DEBUG - POSITION MONITORING: Fresh analysis for CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:55,819 - main - INFO - [FAST] Analyzing token: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:55,819 - main - INFO - [LAUNCH] BYPASSING RATE LIMIT for DexScreener API call: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:55,820 - main - DEBUG - Using cached SOL price: $133.72 (age: 59.2s)
2025-06-23 16:50:55,845 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump HTTP/1.1" 200 None
2025-06-23 16:50:55,856 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 16:50:55,856 - main - INFO - Fetched data - SOL price: $133.72, Supply: 1,000,000,000
2025-06-23 16:50:55,856 - main - INFO - DexScreener data available - Price: $1.389e-05, Volume 24h: $23,067
2025-06-23 16:50:55,857 - main - INFO - [SCAN] Looking for pump.fun pool for CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:50:55,858 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:56,785 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:56,799 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:57,785 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:57,800 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:50:58,826 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:50:58,837 - main - DEBUG - Pool balance success: 30.355757 SOL
2025-06-23 16:50:58,838 - main - INFO - [SUCCESS] Found pool address: BP9nFjp4cZ6JQYGtgidS3TXoo7ovE6mXSN5UdMmCqopb
2025-06-23 16:50:58,839 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:51:00,036 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:51:00,050 - main - DEBUG - Pool balance success: 30.355757 SOL
2025-06-23 16:51:00,050 - main - INFO - [MONEY] Pool SOL balance: 30.355756796
2025-06-23 16:51:00,050 - main - INFO - [CHART] Calculated liquidity: $7,915 (SOL: 30.355756796 * $133.72 * 1.95)
2025-06-23 16:51:00,051 - main - INFO - Found pool BP9nFjp4... with 30.355757 SOL
2025-06-23 16:51:00,051 - main - INFO - Using calculated liquidity: $7,915 (x1.95)
2025-06-23 16:51:00,052 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:51:01,036 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:51:01,050 - main - DEBUG - Holders success: 20 holders
2025-06-23 16:51:01,053 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:51:02,111 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:51:02,136 - main - INFO - [FAST] Analysis completed in 6.32s
2025-06-23 16:51:02,138 - main - INFO - [SCAN] MULTIPLIER CHECK ENABLED: Checking CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump for high multipliers (3x+)
2025-06-23 16:51:02,139 - main - INFO - [CHART] PRICE CHANGES: 5m: 239.0%, 1h: 239.0%, 6h: 239.0%, 24h: 239.0% | Max: 239.0%
2025-06-23 16:51:02,140 - main - INFO - [WARNING] 3-4X MULTIPLIER TOKEN: CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump - 239.0% gain (allowing as it's <= 5x)
2025-06-23 16:51:02,154 - main - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:51:02,155 - main - INFO - [SUCCESS] RUG PROTECTION: All checks passed - Liq: $7,915, MC: $13,897, Whale: 21.1%
2025-06-23 16:51:02,156 - main - INFO - [SUCCESS] Fast analysis result: Price=$0.00001389, MC=$13,897, Liq=$7,915
2025-06-23 16:51:02,161 - main - DEBUG - PERMANENT STATE: 1 total, 0 skipped
2025-06-23 16:51:02,162 - main - DEBUG - Permanent decision recorded for CdPWwNAEHE1jXLtXgAdXjQ7StWTsynaDpmTZJDd2pump
2025-06-23 16:51:02,170 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:51:03,146 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:51:34,593 - main - INFO - [FAST] Analyzing token: BmCfAxyHqHKxio3NCXND3cveow4KmHvSHWYWRYaT5yZj
2025-06-23 16:51:34,596 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:51:34,658 - urllib3.connectionpool - DEBUG - https://api.coingecko.com:443 "GET /api/v3/simple/price?ids=solana&vs_currencies=usd HTTP/1.1" 200 None
2025-06-23 16:51:34,667 - main - INFO - Fetched fresh SOL price: $133.7 (attempt 1)
2025-06-23 16:51:34,959 - urllib3.connectionpool - DEBUG - https://api.dexscreener.com:443 "GET /token-pairs/v1/solana/BmCfAxyHqHKxio3NCXND3cveow4KmHvSHWYWRYaT5yZj HTTP/1.1" 200 None
2025-06-23 16:51:34,976 - main - DEBUG - DexScreener success (attempt 1): 1 pairs found
2025-06-23 16:51:35,756 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:51:35,769 - main - DEBUG - Token supply success: 1,000,000,000
2025-06-23 16:51:35,770 - main - INFO - Fetched data - SOL price: $133.7, Supply: 1,000,000,000
2025-06-23 16:51:35,771 - main - INFO - DexScreener data available - Price: $1.212e-05, Volume 24h: $13,058
2025-06-23 16:51:35,771 - main - INFO - [SCAN] Looking for pump.fun pool for BmCfAxyHqHKxio3NCXND3cveow4KmHvSHWYWRYaT5yZj
2025-06-23 16:51:35,773 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:51:36,970 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:51:36,991 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:51:38,139 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:51:38,162 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:51:39,339 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:51:39,359 - main - DEBUG - Pool balance success: 24.663793 SOL
2025-06-23 16:51:39,360 - main - INFO - [SUCCESS] Found pool address: FqHXZsHAY5fNLUNGrKKAYyAvJ9fVKRhySuWuGzBQMo8V
2025-06-23 16:51:39,363 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:51:40,588 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:51:40,610 - main - DEBUG - Pool balance success: 22.445522 SOL
2025-06-23 16:51:40,610 - main - INFO - [MONEY] Pool SOL balance: 22.445521822
2025-06-23 16:51:40,611 - main - INFO - [CHART] Calculated liquidity: $5,852 (SOL: 22.445521822 * $133.7 * 1.95)
2025-06-23 16:51:40,611 - main - INFO - Found pool FqHXZsHA... with 22.445522 SOL
2025-06-23 16:51:40,612 - main - INFO - Using calculated liquidity: $5,852 (x1.95)
2025-06-23 16:51:40,616 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:51:41,818 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:51:41,832 - main - DEBUG - Holders success: 20 holders
2025-06-23 16:51:41,837 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): mainnet.helius-rpc.com:443
2025-06-23 16:51:42,976 - urllib3.connectionpool - DEBUG - https://mainnet.helius-rpc.com:443 "POST /?api-key=6ae4dea2-9b8d-4dc0-b262-c1ad39cbc1f5 HTTP/1.1" 200 None
2025-06-23 16:51:42,989 - main - INFO - [FAST] Analysis completed in 8.40s
2025-06-23 16:51:42,991 - main - INFO - [SCAN] MULTIPLIER CHECK ENABLED: Checking BmCfAxyHqHKxio3NCXND3cveow4KmHvSHWYWRYaT5yZj for high multipliers (3x+)
2025-06-23 16:51:42,991 - main - INFO - [AIM] GMGN SIGNAL DETECTED: Allowing high multipliers for BmCfAxyHqHKxio3NCXND3cveow4KmHvSHWYWRYaT5yZj
2025-06-23 16:51:42,992 - main - INFO - [AIM] GMGN signals are exempt from multiplier restrictions
2025-06-23 16:51:42,996 - main - INFO - [SHIELD] EXECUTING RUG PROTECTION CHECKS for BmCfAxyHqHKxio3NCXND3cveow4KmHvSHWYWRYaT5yZj
2025-06-23 16:51:42,997 - main - WARNING - [ALERT] RUG PROTECTION TRIGGERED: Low liquidity $5,852 < $7,000
