{"timestamp": "2025-06-22T10:06:56.128459+00:00", "error_type": "main", "error_message": "Failed to fetch SOL price (attempt 1): HTTPSConnectionPool(host='api.coingecko.com', port=443): Read timed out. (read timeout=8)", "module": "main", "function": "get_sol_price", "severity": "error", "context": {"filename": "main.py", "lineno": 283, "levelname": "ERROR"}}
{"timestamp": "2025-06-22T10:10:08.388139+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 9689.286680", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 523, "levelname": "ERROR"}}
{"timestamp": "2025-06-22T10:10:08.392088+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 524, "levelname": "ERROR"}}
{"timestamp": "2025-06-22T10:10:08.394945+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump [Context: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-22T10:10:14.040188+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 9689.286680", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 523, "levelname": "ERROR"}}
{"timestamp": "2025-06-22T10:10:14.041903+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 524, "levelname": "ERROR"}}
{"timestamp": "2025-06-22T10:10:14.044639+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump [Context: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-22T10:10:19.450940+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 9621.849245", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 523, "levelname": "ERROR"}}
{"timestamp": "2025-06-22T10:10:19.451625+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 5 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-22T10:10:19.452209+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 524, "levelname": "ERROR"}}
{"timestamp": "2025-06-22T10:10:19.452775+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 6 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-22T10:10:19.454503+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump [Context: 9VmPUjL5BQbZT4ik2d5Q9dV2WwHCuJVVqEa1AzDSpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T07:43:27.795710+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 7687.719312", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 542, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T07:43:27.798066+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 543, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T07:43:27.799141+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for CPc799f2z4zpE74Qdf5jRH7ZnELptAQVG196jgGFpump [Context: CPc799f2z4zpE74Qdf5jRH7ZnELptAQVG196jgGFpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T07:43:31.096006+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 7687.719312", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 542, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T07:43:31.096803+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 543, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T07:43:31.097541+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for CPc799f2z4zpE74Qdf5jRH7ZnELptAQVG196jgGFpump [Context: CPc799f2z4zpE74Qdf5jRH7ZnELptAQVG196jgGFpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T07:43:37.192668+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 7687.719312", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 542, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T07:43:37.193501+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 5 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T07:43:37.194171+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 543, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T07:43:37.194648+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 6 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T07:43:37.195493+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for CPc799f2z4zpE74Qdf5jRH7ZnELptAQVG196jgGFpump [Context: CPc799f2z4zpE74Qdf5jRH7ZnELptAQVG196jgGFpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:02:43.979364+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 3588.247741", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 533, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:02:43.981691+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 534, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:02:43.982513+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS [Context: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:02:47.400890+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 3588.247741", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 533, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:02:47.401677+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 534, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:02:47.402708+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS [Context: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:02:52.642890+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c Transaction FAILED on blockchain: 5M6uhg2oEUcy6emRT67qh2LLVRCcWUMYDgBdZm51QRxgmTcMJ8zLLJ41yqmbtRrNon1Hjh1CukYxW66E31R42v6X - {'InstructionError': [3, {'Custom': 6022}]}", "module": "pumpportal_trader", "function": "_verify_transaction_success", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 734, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:02:52.643708+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: Transaction FAILED verification on blockchain: 5M6uhg2oEUcy6emRT67qh2LLVRCcWUMYDgBdZm51QRxgmTcMJ8zLLJ41yqmbtRrNon1Hjh1CukYxW66E31R42v6X", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 900, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:02:56.109048+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c BULLETPROOF: No token balance found for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS! Cannot sell tokens we don't have.", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 399, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:02:59.303972+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c BULLETPROOF: No token balance found for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS! Cannot sell tokens we don't have.", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 399, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:02:59.305176+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS [Context: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:18:14.775536+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c BULLETPROOF: No token balance found for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS! Cannot sell tokens we don't have.", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 399, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:18:18.213386+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c BULLETPROOF: No token balance found for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS! Cannot sell tokens we don't have.", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 399, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:18:21.710683+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c BULLETPROOF: No token balance found for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS! Cannot sell tokens we don't have.", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 399, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:18:21.712125+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS [Context: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:18:23.565282+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c BULLETPROOF: No token balance found for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS! Cannot sell tokens we don't have.", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 399, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:18:27.226740+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c BULLETPROOF: No token balance found for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS! Cannot sell tokens we don't have.", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 399, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:18:27.227361+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 5 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:18:30.668451+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c BULLETPROOF: No token balance found for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS! Cannot sell tokens we don't have.", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 399, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:18:30.669200+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 6 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:18:30.671266+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS [Context: 4ybHVUBC1rSoJDnxrWH1CxFpamfmmBH2XK3b2JpZxPfS]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:41.736376+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 1565.759076", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 537, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:41.737024+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 538, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:41.738122+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for B7QXmQQAYBZ8YQGiKPRePnrL6JxDYai8N9yidshRpump [Context: B7QXmQQAYBZ8YQGiKPRePnrL6JxDYai8N9yidshRpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:44.621956+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c PumpPortal API Error Details:", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 831, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:44.622598+00:00", "error_type": "pumpportal_trader", "error_message": "   Status Code: 400", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 832, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:44.623371+00:00", "error_type": "pumpportal_trader", "error_message": "   Response Text: Bad Request", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 833, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:44.624027+00:00", "error_type": "pumpportal_trader", "error_message": "   Request URL: https://pumpportal.fun/api/trade-local", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 834, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:44.624539+00:00", "error_type": "pumpportal_trader", "error_message": "   Request Payload: {'publicKey': '75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA', 'action': 'sell', 'mint': 'B7QXmQQAYBZ8YQGiKPRePnrL6JxDYai8N9yidshRpump', 'amount': '100%', 'denominatedInSol': 'false', 'slippage': 29, 'priorityFee': 0.0001, 'pool': 'auto'}", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 835, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:44.625089+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 5 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:47:48.832552+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c PumpPortal API Error Details:", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 831, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:48.833166+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 6 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:47:48.833949+00:00", "error_type": "pumpportal_trader", "error_message": "   Status Code: 400", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 832, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:48.834444+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 7 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:47:48.834883+00:00", "error_type": "pumpportal_trader", "error_message": "   Response Text: Bad Request", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 833, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:48.835270+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 8 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:47:48.835692+00:00", "error_type": "pumpportal_trader", "error_message": "   Request URL: https://pumpportal.fun/api/trade-local", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 834, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:48.836036+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 9 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:47:48.836550+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 5 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:47:48.836932+00:00", "error_type": "pumpportal_trader", "error_message": "   Request Payload: {'publicKey': '75eUxZsusWRBaVR9y4rZ5sDQTvH5SjnDbbypbNYWmHeA', 'action': 'sell', 'mint': 'B7QXmQQAYBZ8YQGiKPRePnrL6JxDYai8N9yidshRpump', 'amount': '100%', 'denominatedInSol': 'false', 'slippage': 34, 'priorityFee': 0.0001, 'pool': 'auto'}", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 835, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:48.837441+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 ERROR PATTERN ALERT: pumpportal_trader occurred 10 times in 5 minutes", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 112, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:47:48.837847+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 7 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:47:52.062575+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c BULLETPROOF: No token balance found for B7QXmQQAYBZ8YQGiKPRePnrL6JxDYai8N9yidshRpump! Cannot sell tokens we don't have.", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 399, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:52.063339+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 8 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:47:52.065005+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for B7QXmQQAYBZ8YQGiKPRePnrL6JxDYai8N9yidshRpump [Context: B7QXmQQAYBZ8YQGiKPRePnrL6JxDYai8N9yidshRpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:52.065509+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 9 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:47:53.930683+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c BULLETPROOF: No token balance found for B7QXmQQAYBZ8YQGiKPRePnrL6JxDYai8N9yidshRpump! Cannot sell tokens we don't have.", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 399, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:53.931411+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 10 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:47:57.386959+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c BULLETPROOF: No token balance found for B7QXmQQAYBZ8YQGiKPRePnrL6JxDYai8N9yidshRpump! Cannot sell tokens we don't have.", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 399, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:47:57.387693+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 11 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:48:00.584028+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c BULLETPROOF: No token balance found for B7QXmQQAYBZ8YQGiKPRePnrL6JxDYai8N9yidshRpump! Cannot sell tokens we don't have.", "module": "pumpportal_trader", "function": "sell_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 399, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:48:00.584673+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 12 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T08:48:00.585816+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for B7QXmQQAYBZ8YQGiKPRePnrL6JxDYai8N9yidshRpump [Context: B7QXmQQAYBZ8YQGiKPRePnrL6JxDYai8N9yidshRpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T08:48:00.586280+00:00", "error_type": "error_tracker", "error_message": "\ud83d\udea8 CRITICAL ERROR SPIKE: 13 critical errors in last hour", "module": "error_tracker", "function": "_check_error_alerts", "severity": "critical", "context": {"filename": "error_tracker.py", "lineno": 135, "levelname": "CRITICAL"}}
{"timestamp": "2025-06-23T09:04:03.485979+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 140576.829219", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 560, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:04:03.486656+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 561, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:04:03.487853+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for Csi6JSousB25racCdiueRMBFX9srJzUQFnEKoGJqpump [Context: Csi6JSousB25racCdiueRMBFX9srJzUQFnEKoGJqpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:04:09.211214+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c Transaction FAILED on blockchain: 5so6ckYB9haVnvUH8xyGYvMGwY5teqc6fqiYUZhobbKaT8rNPV9J5bhzNcwrMwHPuQbtNzB43BNzJRpiB4s6WJtx - {'InstructionError': [3, {'Custom': 6022}]}", "module": "pumpportal_trader", "function": "_verify_transaction_success", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 761, "levelname": "ERROR"}}
{"timestamp": "2025-06-23T09:04:09.213086+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: Transaction FAILED verification on blockchain: 5so6ckYB9haVnvUH8xyGYvMGwY5teqc6fqiYUZhobbKaT8rNPV9J5bhzNcwrMwHPuQbtNzB43BNzJRpiB4s6WJtx", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 927, "levelname": "ERROR"}}
{"timestamp": "2025-06-24T06:55:35.839775+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c Transaction FAILED on blockchain: 3mFjgF9HTG86bPgffg18Eu8yxYXPXNGPuPvstqSxHfKNYCkXTWKhz7xy7z69wsDBdwB6qBxtR9o28CACdDDtGUUT - {'InstructionError': [3, {'Custom': 6002}]}", "module": "pumpportal_trader", "function": "_verify_transaction_success", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 877, "levelname": "ERROR"}}
{"timestamp": "2025-06-24T06:55:35.843017+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: Transaction FAILED verification on blockchain: 3mFjgF9HTG86bPgffg18Eu8yxYXPXNGPuPvstqSxHfKNYCkXTWKhz7xy7z69wsDBdwB6qBxtR9o28CACdDDtGUUT", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 1046, "levelname": "ERROR"}}
{"timestamp": "2025-06-24T06:55:35.849737+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c Buy failed: Transaction failed blockchain verification", "module": "pumpportal_trader", "function": "buy_token", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 332, "levelname": "ERROR"}}
{"timestamp": "2025-06-24T06:55:35.857587+00:00", "error_type": "utils", "error_message": "buy_error: Failed PumpPortal buy command for 7yBjmqNTMhyDscG1VsqBCgCqwxR23tJNXtbArbPGpump [Context: 7yBjmqNTMhyDscG1VsqBCgCqwxR23tJNXtbArbPGpump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-24T06:59:34.793186+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: SELL FAILED - No tokens sold! Balance: 286323.132435", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 579, "levelname": "ERROR"}}
{"timestamp": "2025-06-24T06:59:34.796934+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c This indicates the transaction failed or was fake!", "module": "pumpportal_trader", "function": "_process_successful_sell", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 580, "levelname": "ERROR"}}
{"timestamp": "2025-06-24T06:59:34.804824+00:00", "error_type": "utils", "error_message": "sell_error: Failed PumpPortal sell command for 3H5dPEXaTrbCwjMHrgBFspiaw24W3Gky1nJ4vty2pump [Context: 3H5dPEXaTrbCwjMHrgBFspiaw24W3Gky1nJ4vty2pump]", "module": "utils", "function": "log_error", "severity": "error", "context": {"filename": "utils.py", "lineno": 143, "levelname": "ERROR"}}
{"timestamp": "2025-06-24T06:59:43.825598+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c Transaction FAILED on blockchain: 5WJwAYvwo5e156UnjJLYVbTdkBGmQ7vKkMDoAvXL49hYmf4fDNLBxgxWhbwr6MgEd7HTJrTRTC1BuphuzxTFDDcj - {'InstructionError': [3, {'Custom': 6022}]}", "module": "pumpportal_trader", "function": "_verify_transaction_success", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 877, "levelname": "ERROR"}}
{"timestamp": "2025-06-24T06:59:43.827739+00:00", "error_type": "pumpportal_trader", "error_message": "\u274c CRITICAL: Transaction FAILED verification on blockchain: 5WJwAYvwo5e156UnjJLYVbTdkBGmQ7vKkMDoAvXL49hYmf4fDNLBxgxWhbwr6MgEd7HTJrTRTC1BuphuzxTFDDcj", "module": "pumpportal_trader", "function": "_execute_trade", "severity": "error", "context": {"filename": "pumpportal_trader.py", "lineno": 1046, "levelname": "ERROR"}}
