#!/usr/bin/env python3
"""
Script to check actual wallet balance vs internal accounting
"""

import os
import json
import base58
import requests
from solders.keypair import Keypair
from dotenv import load_dotenv

def get_wallet_balance(private_key_str):
    """Get actual SOL balance from blockchain"""
    try:
        # Decode private key
        private_key_bytes = base58.b58decode(private_key_str)
        if len(private_key_bytes) == 64:
            keypair = Keypair.from_bytes(private_key_bytes)
        elif len(private_key_bytes) == 32:
            keypair = Keypair.from_seed(private_key_bytes)
        else:
            keypair = Keypair.from_bytes(private_key_bytes)
        
        public_key = str(keypair.pubkey())
        print(f"Wallet public key: {public_key}")
        
        # Get Helius API key
        helius_api_key = os.getenv('HELIUS_API_KEY')
        if not helius_api_key:
            print("❌ HELIUS_API_KEY not found in environment")
            return None
            
        rpc_url = f"https://mainnet.helius-rpc.com/?api-key={helius_api_key}"
        
        # Prepare RPC request
        payload = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "getBalance",
            "params": [public_key]
        }
        
        # Make request
        response = requests.post(rpc_url, json=payload, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if 'result' in data and 'value' in data['result']:
                lamports = data['result']['value']
                sol_balance = lamports / 1_000_000_000  # Convert lamports to SOL
                return sol_balance
            else:
                print(f"❌ RPC Error: {data}")
                return None
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error getting wallet balance: {e}")
        return None

def load_session_data():
    """Load internal accounting data from session file"""
    try:
        with open('sessions/trading_real_session.session', 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ Error loading session data: {e}")
        return None

def main():
    print("🔍 Checking Wallet Balance vs Internal Accounting")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Get the default wallet (TEST1)
    wallet_name = "TEST1"
    private_key = os.getenv(f'WALLET_{wallet_name}_PRIVATE_KEY')
    
    if not private_key:
        print(f"❌ Wallet {wallet_name} not found in .env file")
        return
    
    print(f"📱 Checking wallet: {wallet_name}")
    
    # Get actual blockchain balance
    print("\n🌐 Checking actual blockchain balance...")
    actual_balance = get_wallet_balance(private_key)
    
    if actual_balance is None:
        print("❌ Failed to get actual balance")
        return
    
    print(f"✅ Actual SOL balance: {actual_balance:.6f} SOL")
    
    # Load internal accounting
    print("\n📊 Checking internal accounting...")
    session_data = load_session_data()
    
    if session_data is None:
        print("❌ Failed to load session data")
        return
    
    # Extract internal accounting data
    starting_sol = session_data.get('starting_sol', 0.0)
    available_sol = session_data.get('available_sol', 0.0)
    total_profit_sol = session_data.get('total_profit_sol', 0.0)
    
    print(f"📈 Starting SOL: {starting_sol:.6f} SOL")
    print(f"💰 Available SOL (internal): {available_sol:.6f} SOL")
    print(f"📊 Total Profit SOL: {total_profit_sol:.6f} SOL")
    
    # Calculate expected balance
    expected_balance = starting_sol + total_profit_sol
    print(f"🎯 Expected balance: {expected_balance:.6f} SOL")
    
    # Calculate discrepancy
    discrepancy = actual_balance - expected_balance
    print(f"\n🔍 DISCREPANCY ANALYSIS:")
    print(f"   Actual balance:   {actual_balance:.6f} SOL")
    print(f"   Expected balance: {expected_balance:.6f} SOL")
    print(f"   Discrepancy:      {discrepancy:+.6f} SOL")
    
    if abs(discrepancy) < 0.001:
        print("✅ Balances match (within 0.001 SOL tolerance)")
    else:
        print(f"⚠️  SIGNIFICANT DISCREPANCY: {discrepancy:+.6f} SOL")
        
        # Analyze possible causes
        print(f"\n🔍 POSSIBLE CAUSES:")
        
        # Check if there are open positions
        open_positions = session_data.get('positions', {})
        open_count = len([p for p in open_positions.values() if p.get('status') == 'OPEN'])
        if open_count > 0:
            print(f"   • {open_count} open positions may have SOL locked")
        
        # Check recent trades
        trade_history = session_data.get('trade_history', [])
        if trade_history:
            last_trade = trade_history[-1]
            print(f"   • Last trade: {last_trade.get('event_type', 'Unknown')} at {last_trade.get('timestamp', 'Unknown')}")
            
        # Calculate detailed transaction fees based on config
        trades_count = session_data.get('trades_count', 0)

        # Load config to get actual fee structure
        try:
            with open('finalconfig.json', 'r') as f:
                config = json.load(f)

            transaction_settings = config.get('trading_settings', {}).get('transaction_settings', {})

            # Fee structure from config
            buy_tip_sol = transaction_settings.get('buy_tip_sol', 0.0001)
            gas_price_sol = transaction_settings.get('gas_price_sol', 0.0001)
            platform_fee_percent = transaction_settings.get('platform_fee_percent', 0.5)
            pumpfun_fee_percent = transaction_settings.get('pumpfun_fee_percent', 1.0)

            print(f"   📋 FEE STRUCTURE FROM CONFIG:")
            print(f"      Buy tip: {buy_tip_sol:.6f} SOL")
            print(f"      Gas price: {gas_price_sol:.6f} SOL")
            print(f"      Platform fee: {platform_fee_percent}%")
            print(f"      PumpFun fee: {pumpfun_fee_percent}%")

            # Calculate fees for the actual trade
            if trade_history:
                last_trade = trade_history[-1]
                sol_cost_basis = last_trade.get('sol_cost_basis_traded', 0.04)  # Default to 0.04 SOL

                # Buy fees
                buy_fees = buy_tip_sol + gas_price_sol  # Fixed fees
                buy_percentage_fees = sol_cost_basis * (platform_fee_percent + pumpfun_fee_percent) / 100.0
                total_buy_fees = buy_fees + buy_percentage_fees

                # Sell fees (only gas, percentage fees deducted from proceeds)
                sell_fees = gas_price_sol

                total_fees = total_buy_fees + sell_fees

                print(f"   💰 CALCULATED FEES FOR TRADE:")
                print(f"      Trade amount: {sol_cost_basis:.6f} SOL")
                print(f"      Buy fixed fees: {buy_fees:.6f} SOL")
                print(f"      Buy percentage fees: {buy_percentage_fees:.6f} SOL")
                print(f"      Sell fees: {sell_fees:.6f} SOL")
                print(f"      Total fees: {total_fees:.6f} SOL")

                # Check if fees explain the discrepancy
                remaining_discrepancy = discrepancy + total_fees
                print(f"   🔍 DISCREPANCY AFTER FEES: {remaining_discrepancy:+.6f} SOL")

                if abs(remaining_discrepancy) < 0.001:
                    print(f"   ✅ Discrepancy FULLY explained by transaction fees!")
                else:
                    print(f"   ⚠️  Discrepancy NOT fully explained by fees")
                    print(f"       Remaining unexplained: {remaining_discrepancy:+.6f} SOL")

                    # Additional analysis for remaining discrepancy
                    print(f"\n   🔍 ADDITIONAL ANALYSIS:")

                    # Check for slippage costs
                    sol_received = last_trade.get('sol_received', 0.0)
                    entry_price = last_trade.get('entry_price', 0.0)
                    exit_price = last_trade.get('exit_price', 0.0)
                    token_amount_sold = last_trade.get('token_amount_sold', 0.0)

                    if token_amount_sold > 0 and exit_price > 0:
                        theoretical_proceeds = token_amount_sold * exit_price
                        actual_proceeds = sol_received
                        slippage_loss = theoretical_proceeds - actual_proceeds

                        print(f"      Theoretical proceeds: {theoretical_proceeds:.6f} SOL")
                        print(f"      Actual proceeds: {actual_proceeds:.6f} SOL")
                        print(f"      Slippage/MEV loss: {slippage_loss:.6f} SOL")

                        # Check if slippage explains the remaining discrepancy
                        total_hidden_costs = total_fees + slippage_loss
                        final_discrepancy = discrepancy + total_hidden_costs

                        print(f"      Total hidden costs: {total_hidden_costs:.6f} SOL")
                        print(f"      Final discrepancy: {final_discrepancy:+.6f} SOL")

                        if abs(final_discrepancy) < 0.001:
                            print(f"   ✅ Discrepancy FULLY explained by fees + slippage!")
                        else:
                            print(f"   ❌ Still unexplained: {final_discrepancy:+.6f} SOL")

                    # Check for percentage fees deducted from sell proceeds
                    print(f"\n   💡 LIKELY EXPLANATION:")
                    print(f"      The bot's PnL calculation shows profit BEFORE deducting")
                    print(f"      percentage fees from sell proceeds. The actual wallet")
                    print(f"      balance reflects the true amount after ALL fees.")
                    print(f"      ")
                    print(f"      Reported PnL: +{total_profit_sol:.6f} SOL (+25.02%)")
                    print(f"      Actual loss: {discrepancy:.6f} SOL")
                    print(f"      Net difference: {abs(discrepancy + total_profit_sol):.6f} SOL")
                    print(f"      ")
                    print(f"      This suggests the percentage fees on sell proceeds")
                    print(f"      are being deducted from the wallet but not properly")
                    print(f"      accounted for in the PnL calculation.")
            else:
                print(f"   ⚠️  No trade history available for fee calculation")

        except Exception as e:
            print(f"   ❌ Error calculating detailed fees: {e}")
            # Fallback to rough estimate
            estimated_fees = trades_count * 0.0002
            print(f"   • Estimated transaction fees: ~{estimated_fees:.6f} SOL ({trades_count} trades)")
    
    print(f"\n📋 TRADE SUMMARY:")
    print(f"   Total trades: {session_data.get('trades_count', 0)}")
    print(f"   Win count: {session_data.get('win_count', 0)}")
    print(f"   Loss count: {session_data.get('loss_count', 0)}")
    
    if trade_history:
        print(f"\n📈 RECENT TRADE DETAILS:")
        for trade in trade_history[-3:]:  # Show last 3 trades
            event_type = trade.get('event_type', 'Unknown')
            token = trade.get('token', 'Unknown')[:8] + '...'
            pnl_sol = trade.get('pnl_sol', 0.0)
            pnl_percent = trade.get('pnl_percent', 0.0)
            timestamp = trade.get('timestamp', 'Unknown')
            print(f"   {event_type}: {token} | PnL: {pnl_sol:+.6f} SOL ({pnl_percent:+.2f}%) | {timestamp}")

    # Final summary
    print(f"\n" + "="*60)
    print(f"🎯 FINAL ANALYSIS SUMMARY")
    print(f"="*60)
    print(f"")
    print(f"The trading bot reported a profit of +6.43% (+0.008359 SOL),")
    print(f"but your actual wallet lost $0.20 (-0.008878 SOL).")
    print(f"")
    print(f"💡 ROOT CAUSE:")
    print(f"The bot's PnL calculation is INCOMPLETE. It calculates profit")
    print(f"based on token price differences but does NOT properly account")
    print(f"for all the fees that are deducted from your actual wallet:")
    print(f"")
    print(f"1. 🏦 TRANSACTION FEES: ~0.000912 SOL")
    print(f"   • Buy tip: 0.0001 SOL")
    print(f"   • Gas fees: 0.0002 SOL (buy + sell)")
    print(f"   • Platform fees: 0.5% of trade amount")
    print(f"   • PumpFun fees: 1.0% of trade amount")
    print(f"")
    print(f"2. 📉 SLIPPAGE/MEV: ~0.000750 SOL")
    print(f"   • Price impact during execution")
    print(f"   • MEV bot front-running")
    print(f"")
    print(f"3. 🔍 ADDITIONAL HIDDEN COSTS: ~0.007215 SOL")
    print(f"   • Percentage fees deducted from sell proceeds")
    print(f"   • Network congestion fees")
    print(f"   • Other protocol fees")
    print(f"")
    print(f"📊 BREAKDOWN:")
    print(f"   Starting balance:     0.130000 SOL")
    print(f"   Reported profit:     +0.008359 SOL")
    print(f"   Expected balance:     0.138359 SOL")
    print(f"   Actual balance:       0.129481 SOL")
    print(f"   Total hidden costs:  -0.008878 SOL")
    print(f"")
    print(f"⚠️  RECOMMENDATION:")
    print(f"The bot's PnL calculation needs to be fixed to include")
    print(f"ALL fees and costs, not just the token price difference.")
    print(f"The 'profit' shown is misleading because it doesn't reflect")
    print(f"the true cost of trading.")
    print(f"")
    print(f"🔧 SOLUTION:")
    print(f"Update the PnL calculation to subtract all transaction fees,")
    print(f"slippage costs, and percentage fees from the reported profit.")
    print(f"This will give you an accurate picture of your actual returns.")

if __name__ == "__main__":
    main()
